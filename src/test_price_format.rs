#[cfg(test)]
mod tests {
    use crate::engine::trade::{RoundingMode, format_price_with_tick_mode};

    #[test]
    fn test_format_price_with_tick_round() {
        // 测试四舍五入模式
        let result = format_price_with_tick_mode(1.2345, 0.01, RoundingMode::Round);
        assert_eq!(result, "1.23");

        let result = format_price_with_tick_mode(1.2355, 0.01, RoundingMode::Round);
        assert_eq!(result, "1.24");

        let result = format_price_with_tick_mode(100.0, 1.0, RoundingMode::Round);
        assert_eq!(result, "100");
    }

    #[test]
    fn test_format_price_with_tick_ceil() {
        // 测试向上取整模式
        let result = format_price_with_tick_mode(1.2301, 0.01, RoundingMode::Ceil);
        assert_eq!(result, "1.24");

        let result = format_price_with_tick_mode(1.23, 0.01, RoundingMode::Ceil);
        assert_eq!(result, "1.23");

        let result = format_price_with_tick_mode(99.1, 1.0, RoundingMode::Ceil);
        assert_eq!(result, "100");
    }

    #[test]
    fn test_format_price_with_tick_floor() {
        // 测试向下取整模式
        let result = format_price_with_tick_mode(1.2399, 0.01, RoundingMode::Floor);
        assert_eq!(result, "1.23");

        let result = format_price_with_tick_mode(1.23, 0.01, RoundingMode::Floor);
        assert_eq!(result, "1.23");

        let result = format_price_with_tick_mode(99.9, 1.0, RoundingMode::Floor);
        assert_eq!(result, "99");
    }

    #[test]
    fn test_different_tick_sizes() {
        // 测试不同的 tick 大小
        let result = format_price_with_tick_mode(1.23456, 0.001, RoundingMode::Round);
        assert_eq!(result, "1.235");

        let result = format_price_with_tick_mode(1.23456, 0.0001, RoundingMode::Round);
        assert_eq!(result, "1.2346");

        let result = format_price_with_tick_mode(1.23456, 0.1, RoundingMode::Round);
        assert_eq!(result, "1.2");
    }

    #[test]
    fn test_negative_prices() {
        // 测试负数价格
        let result = format_price_with_tick_mode(-1.2345, 0.01, RoundingMode::Round);
        assert_eq!(result, "-1.23");

        let result = format_price_with_tick_mode(-1.2355, 0.01, RoundingMode::Round);
        assert_eq!(result, "-1.24");
    }

    #[test]
    fn test_zero_and_small_values() {
        // 测试零值和小数值
        let result = format_price_with_tick_mode(0.0, 0.01, RoundingMode::Round);
        assert_eq!(result, "0");

        let result = format_price_with_tick_mode(0.001, 0.01, RoundingMode::Round);
        assert_eq!(result, "0");

        let result = format_price_with_tick_mode(0.005, 0.01, RoundingMode::Round);
        assert_eq!(result, "0.01");
    }

    #[test]
    fn test_performance_comparison() {
        use std::time::Instant;

        let price = 1234.5678_f64;
        let tick = 0.01_f64;
        let iterations = 100_000;

        // 测试新的 ryu 优化函数
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = format_price_with_tick_mode(price, tick, RoundingMode::Round);
        }
        let ryu_duration = start.elapsed();

        // 测试原始的 format! 方法
        let start = Instant::now();
        for _ in 0..iterations {
            let adjusted_price = (price / tick).round() * tick;
            let _ = format!("{:.2}", adjusted_price);
        }
        let format_duration = start.elapsed();

        // 测试纯 ryu 方法（作为基准）
        let start = Instant::now();
        for _ in 0..iterations {
            let adjusted_price = (price / tick).round() * tick;
            let mut ryu_buf = ryu::Buffer::new();
            let _ = ryu_buf.format(adjusted_price);
        }
        let pure_ryu_duration = start.elapsed();

        println!("ryu 优化函数耗时: {:?}", ryu_duration);
        println!("format! 耗时: {:?}", format_duration);
        println!("纯 ryu 耗时: {:?}", pure_ryu_duration);
        println!(
            "相比 format! 性能提升: {:.2}x",
            format_duration.as_nanos() as f64 / ryu_duration.as_nanos() as f64
        );
        println!(
            "相比纯 ryu 开销: {:.2}x",
            ryu_duration.as_nanos() as f64 / pure_ryu_duration.as_nanos() as f64
        );

        // ryu 优化函数应该比 format! 更快
        assert!(ryu_duration < format_duration);
    }
}
