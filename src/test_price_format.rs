#[cfg(test)]
mod tests {
    use crate::engine::trade::{RoundingMode, format_price_with_tick_mode};

    #[test]
    fn test_format_price_with_tick_round() {
        // 测试四舍五入模式
        let result = format_price_with_tick_mode(1.2345, 0.01, RoundingMode::Round);
        assert_eq!(result, "1.23");
        
        let result = format_price_with_tick_mode(1.2355, 0.01, RoundingMode::Round);
        assert_eq!(result, "1.24");
        
        let result = format_price_with_tick_mode(100.0, 1.0, RoundingMode::Round);
        assert_eq!(result, "100");
    }

    #[test]
    fn test_format_price_with_tick_ceil() {
        // 测试向上取整模式
        let result = format_price_with_tick_mode(1.2301, 0.01, RoundingMode::Ceil);
        assert_eq!(result, "1.24");
        
        let result = format_price_with_tick_mode(1.23, 0.01, RoundingMode::Ceil);
        assert_eq!(result, "1.23");
        
        let result = format_price_with_tick_mode(99.1, 1.0, RoundingMode::Ceil);
        assert_eq!(result, "100");
    }

    #[test]
    fn test_format_price_with_tick_floor() {
        // 测试向下取整模式
        let result = format_price_with_tick_mode(1.2399, 0.01, RoundingMode::Floor);
        assert_eq!(result, "1.23");
        
        let result = format_price_with_tick_mode(1.23, 0.01, RoundingMode::Floor);
        assert_eq!(result, "1.23");
        
        let result = format_price_with_tick_mode(99.9, 1.0, RoundingMode::Floor);
        assert_eq!(result, "99");
    }

    #[test]
    fn test_different_tick_sizes() {
        // 测试不同的 tick 大小
        let result = format_price_with_tick_mode(1.23456, 0.001, RoundingMode::Round);
        assert_eq!(result, "1.235");
        
        let result = format_price_with_tick_mode(1.23456, 0.0001, RoundingMode::Round);
        assert_eq!(result, "1.2346");
        
        let result = format_price_with_tick_mode(1.23456, 0.1, RoundingMode::Round);
        assert_eq!(result, "1.2");
    }

    #[test]
    fn test_negative_prices() {
        // 测试负数价格
        let result = format_price_with_tick_mode(-1.2345, 0.01, RoundingMode::Round);
        assert_eq!(result, "-1.23");
        
        let result = format_price_with_tick_mode(-1.2355, 0.01, RoundingMode::Round);
        assert_eq!(result, "-1.24");
    }

    #[test]
    fn test_zero_and_small_values() {
        // 测试零值和小数值
        let result = format_price_with_tick_mode(0.0, 0.01, RoundingMode::Round);
        assert_eq!(result, "0");
        
        let result = format_price_with_tick_mode(0.001, 0.01, RoundingMode::Round);
        assert_eq!(result, "0");
        
        let result = format_price_with_tick_mode(0.005, 0.01, RoundingMode::Round);
        assert_eq!(result, "0.01");
    }

    #[test]
    fn test_performance_comparison() {
        use std::time::Instant;
        
        let price = 1234.5678;
        let tick = 0.01;
        let iterations = 100_000;
        
        // 测试新的优化函数
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = format_price_with_tick_mode(price, tick, RoundingMode::Round);
        }
        let optimized_duration = start.elapsed();
        
        // 测试原始的 format! 方法
        let start = Instant::now();
        for _ in 0..iterations {
            let adjusted_price = (price / tick).round() * tick;
            let _ = format!("{:.2}", adjusted_price);
        }
        let format_duration = start.elapsed();
        
        println!("优化函数耗时: {:?}", optimized_duration);
        println!("format! 耗时: {:?}", format_duration);
        println!("性能提升: {:.2}x", format_duration.as_nanos() as f64 / optimized_duration.as_nanos() as f64);
        
        // 新函数应该更快（或至少不慢太多）
        assert!(optimized_duration < format_duration * 2); // 允许一定的误差范围
    }
}
