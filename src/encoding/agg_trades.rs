// {
//   "e": "aggTrade",    // Event type
//   "E": 1672515782136, // Event time
//   "s": "BNBBTC",      // Symbol
//   "a": 12345,         // Aggregate trade ID
//   "p": "0.001",       // Price
//   "q": "100",         // Quantity
//   "f": 100,           // First trade ID
//   "l": 105,           // Last trade ID
//   "T": 1672515782136, // Trade time
//   "m": true,          // Is the buyer the market maker?
//   "M": true           // Ignore
// }
use std::str::from_utf8_unchecked;

#[derive(Debug, Clone)]
pub struct AggTrade<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub price: f64,
    pub is_buyer_maker: bool,
}

pub fn parse_agg_trade(input: &[u8]) -> Option<AggTrade<'_>> {
    let event_time_pattern = b"\"E\":";
    let start = memchr::memmem::find(input, event_time_pattern)?;
    let start = start + event_time_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let event_time_str = &input[start..start + end];
    let event_time = unsafe { from_utf8_unchecked(event_time_str) }
        .parse()
        .unwrap();

    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'\"', &input[start..])?;
    let symbol = &input[start..start + end];
    let symbol = unsafe { from_utf8_unchecked(symbol) };

    let price_pattern = b"\"p\":\"";
    let start = memchr::memmem::find(input, price_pattern)?;
    let start = start + price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let price_str = &input[start..start + end];
    let price = unsafe { from_utf8_unchecked(price_str) }.parse().unwrap();

    let is_buyer_maker_pattern = b"\"m\":";
    let start = memchr::memmem::find(input, is_buyer_maker_pattern)?;
    let start = start + is_buyer_maker_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let is_buyer_maker_str = &input[start..start + end];
    let is_buyer_maker = unsafe { from_utf8_unchecked(is_buyer_maker_str) }
        .parse()
        .unwrap();

    Some(AggTrade {
        symbol,
        event_time,
        price,
        is_buyer_maker,
    })
}
