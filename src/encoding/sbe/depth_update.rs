/// SBE格式的Depth Update (DepthDiffStreamEvent) 解析
///
/// 基于Binance官方SBE schema实现
/// Stream Name: <symbol>@depth
/// SBE Message Name: DepthDiffStreamEvent
/// Update Speed: 100ms

/// SBE格式的订单簿更新条目
#[derive(Debug, Clone)]
pub struct SbeDepthUpdateLevel {
    pub price: f64,
    pub qty: f64,
}

/// SBE格式的订单簿增量更新
#[derive(Debug)]
pub struct SbeDepthUpdate<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub first_update_id: u64,
    pub final_update_id: u64,
    pub bid_updates: Vec<(f64, f64)>,
    pub ask_updates: Vec<(f64, f64)>,
}

/// 将mantissa和exponent转换为f64
fn mantissa_exponent_to_f64(mantissa: i64, exponent: i8) -> f64 {
    if exponent >= 0 {
        mantissa as f64 * 10_f64.powi(exponent as i32)
    } else {
        mantissa as f64 / 10_f64.powi((-exponent) as i32)
    }
}

pub fn parse_sbe_depth_update(data: &[u8]) -> Option<SbeDepthUpdate<'_>> {
    let data = &data[8..];

    let event_time = u64::from_le_bytes(data[0..8].try_into().unwrap());
    let first_update_id = u64::from_le_bytes(data[8..16].try_into().unwrap());
    let final_update_id = u64::from_le_bytes(data[16..24].try_into().unwrap());
    let price_exponent = data[24] as i8;
    let qty_exponent = data[25] as i8;
    // let block_len_bids = u16::from_le_bytes([data[26], data[27]]);
    let bids_count = u16::from_le_bytes([data[28], data[29]]);

    let mut bid_updates = Vec::with_capacity(bids_count as usize);
    let mut offset = 30;
    for _ in 0..bids_count {
        let end = offset + 8;
        let price_mantissa = i64::from_le_bytes(data[offset..end].try_into().unwrap());
        let qty_end = offset + 16;
        let qty_mantissa = i64::from_le_bytes(data[end..qty_end].try_into().unwrap());
        let price = mantissa_exponent_to_f64(price_mantissa, price_exponent);
        let qty = mantissa_exponent_to_f64(qty_mantissa, qty_exponent);
        bid_updates.push((price, qty));
        offset += 16;
    }

    // let block_len_asks = u16::from_le_bytes([data[offset], data[offset + 1]]);
    let asks_count = u16::from_le_bytes([data[offset + 2], data[offset + 3]]);
    offset += 4;
    let mut ask_updates = Vec::with_capacity(asks_count as usize);
    for _ in 0..asks_count {
        let end = offset + 8;
        let price_mantissa = i64::from_le_bytes(data[offset..end].try_into().unwrap());
        let qty_end = offset + 16;
        let qty_mantissa = i64::from_le_bytes(data[end..qty_end].try_into().unwrap());
        let price = mantissa_exponent_to_f64(price_mantissa, price_exponent);
        let qty = mantissa_exponent_to_f64(qty_mantissa, qty_exponent);
        ask_updates.push((price, qty));
        offset += 16;
    }

    // 解析symbol字段
    let symbol = if offset < data.len() {
        let symbol_length = data[offset] as usize;
        if offset + 1 + symbol_length <= data.len() {
            match std::str::from_utf8(&data[offset + 1..offset + 1 + symbol_length]) {
                Ok(s) => s,
                Err(_) => "UNKNOWN",
            }
        } else {
            "UNKNOWN"
        }
    } else {
        "UNKNOWN"
    };

    Some(SbeDepthUpdate {
        symbol,
        event_time,
        first_update_id,
        final_update_id,
        bid_updates,
        ask_updates,
    })
}
