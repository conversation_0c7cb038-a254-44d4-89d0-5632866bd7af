// 移除未使用的导入，因为我们现在直接实现解析逻辑

/// SBE格式的BookTicker结构（基于Binance官方schema）
#[derive(Debug)]
pub struct SbeBookTicker<'a> {
    pub symbol: &'a str,
    pub bid_price: f64,
    pub bid_qty: f64,
    pub ask_price: f64,
    pub ask_qty: f64,
    pub event_time: u64,
    pub book_update_id: u64,
}

/// Binance SBE BestBidAskStreamEvent 字段偏移量（基于实际数据分析）
/// Template ID: 10001, 实际消息体长度: 58字节（而非schema中的50字节）
mod binance_offsets {
    // 固定字段部分（按照SBE schema顺序）
    pub const EVENT_TIME: usize = 0; // 8字节 - utcTimestampUs (int64)
    pub const BOOK_UPDATE_ID: usize = 8; // 8字节 - updateId (int64)
    pub const PRICE_EXPONENT: usize = 16; // 1字节 - exponent8 (int8)
    pub const QTY_EXPONENT: usize = 17; // 1字节 - exponent8 (int8)
    pub const BID_PRICE: usize = 18; // 8字节 - mantissa64 (int64)
    pub const BID_QTY: usize = 26; // 8字节 - mantissa64 (int64)
    pub const ASK_PRICE: usize = 34; // 8字节 - mantissa64 (int64)
    pub const ASK_QTY: usize = 42; // 8字节 - mantissa64 (int64)
    // 50-57字节：可能是额外字段或填充

    // 变长字段部分（在固定字段之后）
    pub const FIXED_FIELDS_SIZE: usize = 50; // schema中声明的block_length
    pub const SYMBOL_LENGTH_OFFSET: usize = 50; // 1字节 - varString8 length
    pub const SYMBOL_DATA_OFFSET: usize = 51; // 变长 - symbol data
}

/// 将mantissa和exponent转换为f64
fn mantissa_exponent_to_f64(mantissa: i64, exponent: i8) -> f64 {
    if mantissa == 0 {
        return 0.0;
    }

    let base = mantissa as f64;
    let power = 10.0_f64.powi(exponent as i32);
    base * power
}

// #[perf_macro::measure]
#[inline(always)]
pub fn parse_sbe_bookticker(data: &[u8]) -> Option<SbeBookTicker<'_>> {
    use binance_offsets::*;
    let data = &data[8..];

    // 检查数据长度是否足够包含固定字段
    if data.len() < FIXED_FIELDS_SIZE {
        return None;
    }

    // 解析固定字段
    let event_time = i64::from_le_bytes([
        data[EVENT_TIME],
        data[EVENT_TIME + 1],
        data[EVENT_TIME + 2],
        data[EVENT_TIME + 3],
        data[EVENT_TIME + 4],
        data[EVENT_TIME + 5],
        data[EVENT_TIME + 6],
        data[EVENT_TIME + 7],
    ]) as u64;

    let book_update_id = i64::from_le_bytes([
        data[BOOK_UPDATE_ID],
        data[BOOK_UPDATE_ID + 1],
        data[BOOK_UPDATE_ID + 2],
        data[BOOK_UPDATE_ID + 3],
        data[BOOK_UPDATE_ID + 4],
        data[BOOK_UPDATE_ID + 5],
        data[BOOK_UPDATE_ID + 6],
        data[BOOK_UPDATE_ID + 7],
    ]) as u64;

    let price_exponent = data[PRICE_EXPONENT] as i8;
    let qty_exponent = data[QTY_EXPONENT] as i8;

    let bid_price_mantissa = i64::from_le_bytes([
        data[BID_PRICE],
        data[BID_PRICE + 1],
        data[BID_PRICE + 2],
        data[BID_PRICE + 3],
        data[BID_PRICE + 4],
        data[BID_PRICE + 5],
        data[BID_PRICE + 6],
        data[BID_PRICE + 7],
    ]);

    let bid_qty_mantissa = i64::from_le_bytes([
        data[BID_QTY],
        data[BID_QTY + 1],
        data[BID_QTY + 2],
        data[BID_QTY + 3],
        data[BID_QTY + 4],
        data[BID_QTY + 5],
        data[BID_QTY + 6],
        data[BID_QTY + 7],
    ]);

    let ask_price_mantissa = i64::from_le_bytes([
        data[ASK_PRICE],
        data[ASK_PRICE + 1],
        data[ASK_PRICE + 2],
        data[ASK_PRICE + 3],
        data[ASK_PRICE + 4],
        data[ASK_PRICE + 5],
        data[ASK_PRICE + 6],
        data[ASK_PRICE + 7],
    ]);

    let ask_qty_mantissa = i64::from_le_bytes([
        data[ASK_QTY],
        data[ASK_QTY + 1],
        data[ASK_QTY + 2],
        data[ASK_QTY + 3],
        data[ASK_QTY + 4],
        data[ASK_QTY + 5],
        data[ASK_QTY + 6],
        data[ASK_QTY + 7],
    ]);

    // 转换mantissa + exponent为f64
    let bid_price = mantissa_exponent_to_f64(bid_price_mantissa, price_exponent);
    let bid_qty = mantissa_exponent_to_f64(bid_qty_mantissa, qty_exponent);
    let ask_price = mantissa_exponent_to_f64(ask_price_mantissa, price_exponent);
    let ask_qty = mantissa_exponent_to_f64(ask_qty_mantissa, qty_exponent);

    // 解析变长symbol字段
    // 检查是否有足够的数据包含symbol字段
    if data.len() <= SYMBOL_LENGTH_OFFSET {
        // 如果没有symbol字段，使用默认值
        let symbol = "UNKNOWN";

        // 验证数据有效性
        if bid_price <= 0.0 || ask_price <= 0.0 {
            return None;
        }

        return Some(SbeBookTicker {
            symbol,
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
            event_time,
            book_update_id,
        });
    }

    let symbol_length = data[SYMBOL_LENGTH_OFFSET] as usize;

    if data.len() < SYMBOL_DATA_OFFSET + symbol_length {
        return None;
    }

    let symbol_bytes = &data[SYMBOL_DATA_OFFSET..SYMBOL_DATA_OFFSET + symbol_length];
    let symbol = match std::str::from_utf8(symbol_bytes) {
        Ok(s) => s,
        Err(_) => {
            return None;
        }
    };

    // 验证数据有效性
    if symbol.is_empty() || bid_price <= 0.0 || ask_price <= 0.0 {
        return None;
    }

    Some(SbeBookTicker {
        symbol,
        bid_price,
        bid_qty,
        ask_price,
        ask_qty,
        event_time,
        book_update_id,
    })
}
