#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct SbeTrade<'a> {
    pub symbol: &'a str,
    pub trade_time: u64,
    pub event_time: u64,
    pub last_trade_id: u64,
    pub last_trade_price: f64,
    pub last_trade_is_buyer_maker: bool,
}

pub fn parse_sbe_trades(data: &[u8]) -> Option<SbeTrade<'_>> {
    // 首先检查是否有SBE头部
    if data.len() < 8 {
        return None;
    }

    // 解析SBE头部
    let block_length = u16::from_le_bytes([data[0], data[1]]);
    let template_id = u16::from_le_bytes([data[2], data[3]]);
    let _schema_id = u16::from_le_bytes([data[4], data[5]]);
    let _version = u16::from_le_bytes([data[6], data[7]]);

    // 验证这是一个trade消息
    if template_id != 10000 {
        return None;
    }

    // 检查数据长度是否足够包含头部和消息体
    if data.len() < 8 + block_length as usize {
        return None;
    }

    // 提取消息体和变长数据
    let message_body = &data[8..8 + block_length as usize];
    let variable_data = &data[8 + block_length as usize..];

    parse_sbe_trades_body(message_body, variable_data)
}

fn parse_sbe_trades_body<'a>(message_body: &[u8], variable_data: &'a [u8]) -> Option<SbeTrade<'a>> {
    let (event_time, trade_time) = if message_body.len() >= 16 {
        let event_time = u64::from_le_bytes(message_body[..8].try_into().unwrap());
        let trade_time = u64::from_le_bytes(message_body[8..16].try_into().unwrap());
        (event_time, trade_time)
    } else {
        (0u64, 0u64)
    };

    let price_exponent = message_body[16] as i8;

    // 检查变长数据是否存在
    if variable_data.len() < 6 {
        return None;
    }

    let mut offset = 0;

    let first_u16 = u16::from_le_bytes([variable_data[0], variable_data[1]]);
    let second_u32 = u32::from_le_bytes([
        variable_data[2],
        variable_data[3],
        variable_data[4],
        variable_data[5],
    ]);

    let block_length = first_u16;
    let trade_count = second_u32;
    offset += 6;

    if trade_count == 0 {
        return None;
    }

    let record_size: usize = block_length as usize;
    let last_trade_offset = offset + (trade_count - 1) as usize * record_size;
    if last_trade_offset > variable_data.len() {
        return None;
    }
    let trade_record = &variable_data[last_trade_offset..last_trade_offset + record_size];
    let last_trade_id = u64::from_le_bytes([
        trade_record[0],
        trade_record[1],
        trade_record[2],
        trade_record[3],
        trade_record[4],
        trade_record[5],
        trade_record[6],
        trade_record[7],
    ]);
    let price_raw = u64::from_le_bytes([
        trade_record[8],
        trade_record[9],
        trade_record[10],
        trade_record[11],
        trade_record[12],
        trade_record[13],
        trade_record[14],
        trade_record[15],
    ]);

    let last_trade_price = price_raw as f64 * 10f64.powi(price_exponent as i32);

    let flags = if record_size > 24 {
        trade_record[24]
    } else {
        0
    };
    let last_trade_is_buyer_maker = (flags & 0x01) != 0;

    offset += record_size * trade_count as usize;

    // 解析symbol (变长字符串)
    let symbol = if offset < variable_data.len() {
        let symbol_length = variable_data[offset] as usize;
        offset += 1;

        if offset + symbol_length <= variable_data.len() {
            let symbol_bytes = &variable_data[offset..offset + symbol_length];
            match std::str::from_utf8(symbol_bytes) {
                Ok(s) => s,
                Err(_) => "DECODE_ERROR",
            }
        } else {
            "UNKNOWN"
        }
    } else {
        "UNKNOWN"
    };

    Some(SbeTrade {
        symbol,
        event_time,
        trade_time,
        last_trade_id,
        last_trade_price,
        last_trade_is_buyer_maker,
    })
}
