#[derive(Debug)]
pub struct SbeDepthSnapshot<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub last_update_id: u64,
    pub bids: [(f64, f64); 20],
    pub asks: [(f64, f64); 20],
}

/// 将mantissa和exponent转换为f64
fn mantissa_exponent_to_f64(mantissa: i64, exponent: i8) -> f64 {
    if exponent >= 0 {
        mantissa as f64 * 10_f64.powi(exponent as i32)
    } else {
        mantissa as f64 / 10_f64.powi((-exponent) as i32)
    }
}

pub fn parse_sbe_depth_snapshot(data: &[u8]) -> Option<SbeDepthSnapshot<'_>> {
    let data = &data[8..];

    let event_time = u64::from_le_bytes(data[..8].try_into().unwrap());
    let last_update_id = u64::from_le_bytes(data[8..16].try_into().unwrap());
    let price_exponent = data[16] as i8;
    let qty_exponent = data[17] as i8;
    // let block_len_bids = u16::from_le_bytes([data[18], data[19]]);
    let bids_count = u16::from_le_bytes([data[20], data[21]]);

    let mut bids = [(0.0, 0.0); 20];
    let mut offset = 22;
    let mut step = 0;
    for i in 0..bids_count {
        let begin = offset + (8 * step as usize);
        let end = offset + (8 * (step + 1) as usize);
        let price_mantissa = i64::from_le_bytes(data[begin..end].try_into().unwrap());
        let qty_end = offset + (8 * (step + 2) as usize);
        let qty_mantissa = i64::from_le_bytes(data[end..qty_end].try_into().unwrap());
        let price = mantissa_exponent_to_f64(price_mantissa, price_exponent);
        let qty = mantissa_exponent_to_f64(qty_mantissa, qty_exponent);
        bids[i as usize] = (price, qty);
        step += 2;
    }
    offset += bids_count as usize * 16;

    // let block_len_asks = u16::from_le_bytes([data[offset], data[offset + 1]]);
    let asks_count = u16::from_le_bytes([data[offset + 2], data[offset + 3]]);
    offset += 4;
    let mut asks = [(0.0, 0.0); 20];
    let mut step = 0;
    for i in 0..asks_count {
        let begin = offset + (8 * step as usize);
        let end = offset + (8 * (step + 1) as usize);
        let price_mantissa = i64::from_le_bytes(data[begin..end].try_into().unwrap());
        let qty_end = offset + (8 * (step + 2) as usize);
        let qty_mantissa = i64::from_le_bytes(data[end..qty_end].try_into().unwrap());
        let price = mantissa_exponent_to_f64(price_mantissa, price_exponent);
        let qty = mantissa_exponent_to_f64(qty_mantissa, qty_exponent);
        asks[i as usize] = (price, qty);
        step += 2;
    }
    offset += asks_count as usize * 16;

    // 解析symbol字段
    let symbol = if offset < data.len() {
        let symbol_length = data[offset] as usize;
        if offset + 1 + symbol_length <= data.len() {
            match std::str::from_utf8(&data[offset + 1..offset + 1 + symbol_length]) {
                Ok(s) => s,
                Err(_) => "UNKNOWN",
            }
        } else {
            "UNKNOWN"
        }
    } else {
        "UNKNOWN"
    };

    Some(SbeDepthSnapshot {
        symbol,
        event_time,
        last_update_id,
        bids,
        asks,
    })
}
