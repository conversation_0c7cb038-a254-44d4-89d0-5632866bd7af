use memchr;
use std::str;

#[derive(Debug)]
pub struct BookTicker<'a> {
    pub update_id: u64,
    pub symbol: &'a str,
    pub bid_price: f64,
    pub bid_qty: f64,
    pub ask_price: f64,
    pub ask_qty: f64,
}

pub fn parse_bookticker(input: &[u8]) -> Option<BookTicker<'_>> {
    // 0. 查找 "s":"BTCUSDT" 的位置
    let symbol_pattern = b"\"u\":";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let update_id_str = &input[start..start + end];
    // 1. 查找 "s":"BTCUSDT" 的位置
    let symbol_pattern = b"\"s\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let symbol = &input[start..start + end];
    // 2. 查找 "b":0.00000000 的位置
    let input = &input[start + end..];
    let bid_price_pattern = b"\"b\":\"";
    let start = memchr::memmem::find(input, bid_price_pattern)?;
    let start = start + bid_price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let bid_price = &input[start..start + end];
    let input = &input[start + end..];
    // 3. 查找 "B":0.00000000 的位置
    let bid_qty_pattern = b"\"B\":\"";
    let start = memchr::memmem::find(input, bid_qty_pattern)?;
    let start = start + bid_qty_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let bid_qty = &input[start..start + end];
    // 4. 查找 "a":0.00000000 的位置
    let input = &input[start + end..];
    let ask_price_pattern = b"\"a\":\"";
    let start = memchr::memmem::find(input, ask_price_pattern)?;
    let start = start + ask_price_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let ask_price = &input[start..start + end];
    let input = &input[start + end..];
    // 5. 查找 "A":0.00000000 的位置
    let ask_qty_pattern = b"\"A\":\"";
    let start = memchr::memmem::find(input, ask_qty_pattern)?;
    let start = start + ask_qty_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let ask_qty = &input[start..start + end];

    let symbol = unsafe { std::mem::transmute::<&[u8], &str>(symbol) };
    let bid_price: &str = unsafe { std::mem::transmute(bid_price) };
    let ask_price: &str = unsafe { std::mem::transmute(ask_price) };
    let bid_qty: &str = unsafe { std::mem::transmute(bid_qty) };
    let ask_qty: &str = unsafe { std::mem::transmute(ask_qty) };
    let update_id: &str = unsafe { std::mem::transmute(update_id_str) };

    Some(BookTicker {
        update_id: update_id.parse().unwrap(),
        symbol,
        bid_price: bid_price.parse::<f64>().unwrap(),
        bid_qty: bid_qty.parse::<f64>().unwrap(),
        ask_price: ask_price.parse::<f64>().unwrap(),
        ask_qty: ask_qty.parse::<f64>().unwrap(),
    })
}
