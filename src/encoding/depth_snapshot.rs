use memchr;
use std::str;

#[derive(Debug)]
pub struct DepthSnapshot<'a> {
    pub last_update_id: u64,
    pub symbol: &'a str,
    pub bids: [(f64, f64); 1],
    pub asks: [(f64, f64); 1],
}

pub fn parse_depth_snapshot(input: &[u8]) -> Option<DepthSnapshot<'_>> {
    let update_id_pattern = b"\"eId\":";
    let start = memchr::memmem::find(input, update_id_pattern)?;
    let start = start + update_id_pattern.len();
    let end = memchr::memchr(b',', &input[start..])?;
    let update_id_str = &input[start..start + end];

    let symbol_pattern = b"eam\":\"";
    let start = memchr::memmem::find(input, symbol_pattern)?;
    let start = start + symbol_pattern.len();
    let end = memchr::memchr(b'@', &input[start..])?;
    let symbol = &input[start..start + end];

    // 3. 解析 bids 数组
    let bids_pattern = b"\"ds\":[[\"";
    let start = memchr::memmem::find(input, bids_pattern)?;
    let start = start + bids_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let bid1_price = &input[start..start + end];

    // 4. 解析 asks 数组
    let asks_pattern = b"\"ks\":[[\"";
    let start = memchr::memmem::find(input, asks_pattern)?;
    let start = start + asks_pattern.len();
    let end = memchr::memchr(b'"', &input[start..])?;
    let ask1_price = &input[start..start + end];

    let update_id = unsafe { std::mem::transmute::<&[u8], &str>(update_id_str) };
    let symbol = unsafe { std::mem::transmute::<&[u8], &str>(symbol) };
    let bid1_price = unsafe { std::mem::transmute::<&[u8], &str>(bid1_price) };
    let ask1_price = unsafe { std::mem::transmute::<&[u8], &str>(ask1_price) };

    Some(DepthSnapshot {
        last_update_id: update_id.parse().unwrap(),
        symbol,
        bids: [(bid1_price.parse().unwrap(), 0.0); 1],
        asks: [(ask1_price.parse().unwrap(), 0.0); 1],
    })
}
