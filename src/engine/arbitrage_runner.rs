use std::{
    net::{IpAddr, Ipv4Addr, SocketAddr},
    str::FromStr,
};

use crate::{
    CallbackData, EdgeDirection, Message, Result, Settings, TRADING_PAIR_RATES, TradingPair,
    WebSocket, WebSocketHandle,
    encoding::{
        agg_trades::parse_agg_trade,
        book_ticker::parse_bookticker,
        depth_snapshot::parse_depth_snapshot,
        depth_update::parse_depth_update,
        sbe::{
            parse_sbe_bookticker, parse_sbe_depth_snapshot, parse_sbe_depth_update,
            trade::parse_sbe_trades,
        },
    },
    engine::{
        self,
        arbitrage_engine::ArbitrageEngine,
        binance::{
            generate_order_url, generate_sbe_bbo_url, generate_sbe_depth_20_url,
            generate_ws_bbo_url,
        },
        monitor::init_latency_stats,
        token::*,
        trade::{
            generate_order_request_by_symbol, generate_session_logon_request,
            generate_user_data_sub_request, update_trading_fee,
        },
        trading_pair::{API_KEY, TRADING_PAIR_COUNT},
    },
    error, flush_logs, info, logln,
    net::utils::url::Url,
    utils::{self, perf::circles_to_ns},
};

// const REST_HOST: &str = "https://api.binance.com";

pub fn run(
    market_ip: Option<String>,
    _trade_ip: Option<String>,
    depth_ip: Option<String>,
    order_ip: Option<String>,
) -> Result<()> {
    init_latency_stats();

    let mut last_test_order_time: u64 = utils::perf::now();

    const IN_LEN: usize = 1024 * 32;
    const OUT_LEN: usize = 1024 * 4;
    let callback = move |handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
                         cd: CallbackData|
          -> Result<()> {
        let now = utils::perf::now();
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    WS_BBO_1 | WS_BBO_2 | WS_BBO_3 | WS_BBO_4 => {
                        if let Some(bt) = parse_bookticker(data.as_ref()) {
                            if !ArbitrageEngine::update_rate_by_ws_book_ticker(&bt) {
                                return Ok(());
                            }
                            if let Some(ring_index) =
                                ArbitrageEngine::check_arbitrage(now, bt.symbol.into())
                            {
                                if let Some(_) = ArbitrageEngine::compute_orders(ring_index) {
                                    ArbitrageEngine::place_orders(ring_index, now, handle);
                                    logln!("Trigger symbol: {}", bt.symbol);
                                } else {
                                    logln!("no orders");
                                    flush_logs!();
                                }
                            }
                        } else {
                            logln!("failed to parse market data");
                        }
                    }
                    SBE_BBO_1 | SBE_BBO_2 | SBE_BBO_3 | SBE_BBO_4 | SBE_BBO_5 | SBE_BBO_6 => {
                        if let Some(bt) = parse_sbe_bookticker(data.as_ref()) {
                            if !ArbitrageEngine::update_rate(&bt) {
                                return Ok(());
                            }
                            if let Some(ring_index) =
                                ArbitrageEngine::check_arbitrage(now, bt.symbol.into())
                            {
                                if let Some(_) = ArbitrageEngine::compute_orders(ring_index) {
                                    ArbitrageEngine::place_orders(ring_index, now, handle);
                                    logln!("Trigger symbol: {}", bt.symbol);
                                } else {
                                    logln!("no orders");
                                    flush_logs!();
                                }
                            } else if circles_to_ns(now - last_test_order_time) > 2_000_000_000.0 {
                                // 定期发送测试订单以保持连接活跃
                                let pair =
                                    TradingPair::from(now as usize % TRADING_PAIR_COUNT as usize);
                                let price = unsafe {
                                    TRADING_PAIR_RATES[pair as usize]
                                        [EdgeDirection::Reverse as usize]
                                };
                                if price <= 0.0 {
                                    return Ok(());
                                }
                                if !ArbitrageEngine::user_data_subscribed() {
                                    return Ok(());
                                }
                                let buf = handle.get_write_buf(ORDER_TOKEN_1)?;
                                let pair_str = pair.to_str();
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                let buf = handle.get_write_buf(ORDER_TOKEN_2)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                let buf = handle.get_write_buf(ORDER_TOKEN_3)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                let buf = handle.get_write_buf(ORDER_TOKEN_4)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                let written_all = handle.trigger_write(ORDER_TOKEN_1)?;
                                if !written_all {
                                    info!("Reconnect order connection: {:?}", ORDER_TOKEN_1);
                                    flush_logs!();
                                    handle.reconnect_connection(ORDER_TOKEN_1)?;
                                    ArbitrageEngine::unset_user_data_subscribed();
                                    ArbitrageEngine::set_reconnect_time(now);
                                }
                                let written_all = handle.trigger_write(ORDER_TOKEN_2)?;
                                if !written_all {
                                    info!("Reconnect order connection: {:?}", ORDER_TOKEN_2);
                                    flush_logs!();
                                    handle.reconnect_connection(ORDER_TOKEN_2)?;
                                    ArbitrageEngine::unset_user_data_subscribed();
                                    ArbitrageEngine::set_reconnect_time(now);
                                }
                                let written_all = handle.trigger_write(ORDER_TOKEN_3)?;
                                if !written_all {
                                    info!("Reconnect order connection: {:?}", ORDER_TOKEN_3);
                                    handle.reconnect_connection(ORDER_TOKEN_3)?;
                                    ArbitrageEngine::unset_user_data_subscribed();
                                    ArbitrageEngine::set_reconnect_time(now);
                                }
                                let written_all = handle.trigger_write(ORDER_TOKEN_4)?;
                                if !written_all {
                                    info!("Reconnect order connection: {:?}", ORDER_TOKEN_4);
                                    handle.reconnect_connection(ORDER_TOKEN_4)?;
                                    ArbitrageEngine::unset_user_data_subscribed();
                                    ArbitrageEngine::set_reconnect_time(now);
                                }
                                last_test_order_time = now;
                            }
                        } else {
                            info!("failed to parse market data");
                        }
                    }
                    SBE_DEPTH_DF_1 | SBE_DEPTH_DF_2 | SBE_DEPTH_DF_3 | SBE_DEPTH_DF_4 => {
                        if let Some(depth_diff) = parse_sbe_depth_update(data.as_ref()) {
                            // updates are sorted by exchange already
                            if !ArbitrageEngine::update_orderbook_by_updates(&depth_diff) {
                                return Ok(());
                            }
                            if let Some(ring_index) =
                                ArbitrageEngine::check_arbitrage(now, depth_diff.symbol.into())
                            {
                                if let Some(_) = ArbitrageEngine::compute_orders(ring_index) {
                                    ArbitrageEngine::place_orders(ring_index, now, handle);
                                    error!("Trigger symbol: {}", depth_diff.symbol);
                                } else {
                                    error!("no orders");
                                    flush_logs!();
                                }
                            }
                        } else {
                            error!("failed to parse depth diff");
                        }
                    }
                    SBE_DEPTH_SS_1 | SBE_DEPTH_SS_2 | SBE_DEPTH_SS_3 | SBE_DEPTH_SS_4 => {
                        if let Some(depth_snapshot) = parse_sbe_depth_snapshot(data.as_ref()) {
                            if !ArbitrageEngine::update_orderbook_by_snapshot(&depth_snapshot) {
                                return Ok(());
                            }
                            if let Some(ring_index) =
                                ArbitrageEngine::check_arbitrage(now, depth_snapshot.symbol.into())
                            {
                                if let Some(_) = ArbitrageEngine::compute_orders(ring_index) {
                                    ArbitrageEngine::place_orders(ring_index, now, handle);
                                    error!("Trigger symbol: {}", depth_snapshot.symbol);
                                } else {
                                    error!("no orders");
                                    flush_logs!();
                                }
                            }
                        } else {
                            error!("failed to parse depth snapshot");
                        }
                    }
                    WS_DEPTH_SS_1 | WS_DEPTH_SS_2 => {
                        if let Some(depth) = parse_depth_snapshot(data.as_ref()) {
                            if !ArbitrageEngine::update_orderbook_by_ws_snapshot(&depth) {
                                return Ok(());
                            }
                            let pair = TradingPair::from_lower_case(depth.symbol);
                            if let Some(ring_index) = ArbitrageEngine::check_arbitrage(now, pair) {
                                if let Some(_) = ArbitrageEngine::compute_orders(ring_index) {
                                    ArbitrageEngine::place_orders(ring_index, now, handle);
                                    error!("Trigger symbol: {}", depth.symbol);
                                } else {
                                    error!("no orders");
                                    flush_logs!();
                                }
                            }
                        }
                    }
                    WS_DEPTH_DF_T_1 | WS_DEPTH_DF_T_2 | WS_DEPTH_DF_T_3 | WS_DEPTH_DF_T_4 => {
                        flush_logs!();
                        if let Some(depth_diff) = parse_depth_update(data.as_ref()) {
                            // updates are sorted by exchange already
                            if !ArbitrageEngine::update_orderbook_by_ws_updates(&depth_diff) {
                                return Ok(());
                            }
                            if let Some(ring_index) =
                                ArbitrageEngine::check_arbitrage(now, depth_diff.symbol.into())
                            {
                                if let Some(_) = ArbitrageEngine::compute_orders(ring_index) {
                                    ArbitrageEngine::place_orders(ring_index, now, handle);
                                    error!("Trigger symbol: {}", depth_diff.symbol);
                                } else {
                                    error!("no orders");
                                    flush_logs!();
                                }
                            }
                        } else {
                            error!("failed to parse depth diff");
                        }
                    }
                    WS_AGG_TRADE_T_1 | WS_AGG_TRADE_T_2 => {
                        if let Some(trade) = parse_agg_trade(data.as_ref()) {
                            ArbitrageEngine::update_rate_by_agg_trades(trade);
                        } else {
                            error!("failed to parse agg trade");
                        }
                    }
                    TRADE_TOKEN_1 | TRADE_TOKEN_2 | TRADE_TOKEN_3 | TRADE_TOKEN_4 => {
                        if let Some(trade) = parse_sbe_trades(data.as_ref()) {
                            ArbitrageEngine::update_rate_by_trades(trade);
                        } else {
                            error!("failed to parse trade");
                        }
                    }
                    ORDER_TOKEN_1 | ORDER_TOKEN_2 | ORDER_TOKEN_3 | ORDER_TOKEN_4 => {
                        if !ArbitrageEngine::user_data_subscribed() {
                            handle
                                .send_message(token, generate_user_data_sub_request())
                                .unwrap();
                            ArbitrageEngine::set_user_data_subscribed();
                        }
                        match engine::monitor::monitor_order_execution(
                            token.0 - 1,
                            data.as_ref(),
                            handle,
                        ) {
                            Some(edge_index) => {
                                let reconnect_token = match edge_index {
                                    0 => ORDER_TOKEN_1,
                                    1 => ORDER_TOKEN_2,
                                    2 => ORDER_TOKEN_3,
                                    3 => ORDER_TOKEN_4,
                                    5 => token,
                                    _ => panic!("Invalid edge index: {}", edge_index),
                                };
                                info!("Reconnect order connection: {:?}", reconnect_token);
                                handle.reconnect_connection(reconnect_token)?;
                                ArbitrageEngine::unset_user_data_subscribed();
                                ArbitrageEngine::set_reconnect_time(now);
                            }
                            None => {}
                        }
                    }
                    _ => (),
                },
                Message::HttpResponse(response) => {
                    if let Some(body) = response.body.as_ref() {
                        update_trading_fee(body);
                        ArbitrageEngine::set_trading_fee_updated();
                        flush_logs!();
                    }
                }
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                ORDER_TOKEN_1 | ORDER_TOKEN_2 | ORDER_TOKEN_3 | ORDER_TOKEN_4 => {
                    info!("Order connection opened: {:?}", token);
                    handle.send_message(token, generate_session_logon_request())?;
                }
                REST_TOKEN => {
                    if !ArbitrageEngine::get_trading_fee_updated() {
                        info!("quering trading fee");
                        ArbitrageEngine::set_trading_fee_updated();
                    }
                    // handle.send_message(REST_TOKEN, generate_trading_fee_request())?;
                }
                _ => (),
            },
            CallbackData::ConnectionClose(token, err) => {
                info!("connection close: {:?} {:?}", token, err);
                flush_logs!();
            }
            CallbackData::ConnectionError(token, error) => {
                error!("connection err: {:?}: {:?}", token, error);
                flush_logs!();
            }
        }
        Ok(())
    };
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(1));
    let mut websocket = WebSocket::new(settings, callback)?;

    let mut headers = std::collections::HashMap::new();
    headers.insert("X-MBX-APIKEY".to_string(), API_KEY.to_string());

    let mut sbe_bbo_url: Url = generate_sbe_bbo_url(0).into();
    if let Some(ref mip) = market_ip {
        sbe_bbo_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&mip).unwrap()),
            sbe_bbo_url.port,
        ));
    }
    info!("sbe bbo url: {}", sbe_bbo_url);
    websocket.connect_with_headers(sbe_bbo_url.clone(), SBE_BBO_1, headers.clone())?;
    websocket.connect_with_headers(sbe_bbo_url.clone(), SBE_BBO_2, headers.clone())?;
    let mut sbe_bbo_url: Url = generate_sbe_bbo_url(1).into();
    if let Some(ref mip) = market_ip {
        sbe_bbo_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&mip).unwrap()),
            sbe_bbo_url.port,
        ));
    }
    info!("sbe bbo url: {}", sbe_bbo_url);
    websocket.connect_with_headers(sbe_bbo_url.clone(), SBE_BBO_3, headers.clone())?;
    websocket.connect_with_headers(sbe_bbo_url.clone(), SBE_BBO_4, headers.clone())?;
    let mut sbe_bbo_url: Url = generate_sbe_bbo_url(2).into();
    if let Some(ref mip) = market_ip {
        sbe_bbo_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&mip).unwrap()),
            sbe_bbo_url.port,
        ));
    }
    info!("sbe bbo url: {}", sbe_bbo_url);
    websocket.connect_with_headers(sbe_bbo_url.clone(), SBE_BBO_5, headers.clone())?;
    websocket.connect_with_headers(sbe_bbo_url.clone(), SBE_BBO_6, headers.clone())?;

    let ws_bbo_url: Url = generate_ws_bbo_url(0).into();
    info!("ws bbo url: {}", ws_bbo_url);
    websocket.connect_with_headers(ws_bbo_url.clone(), WS_BBO_1, headers.clone())?;
    websocket.connect_with_headers(ws_bbo_url.clone(), WS_BBO_2, headers.clone())?;
    let ws_bbo_url: Url = generate_ws_bbo_url(1).into();
    info!("ws bbo url: {}", ws_bbo_url);
    websocket.connect_with_headers(ws_bbo_url.clone(), WS_BBO_3, headers.clone())?;
    websocket.connect_with_headers(ws_bbo_url.clone(), WS_BBO_4, headers.clone())?;

    // let mut sbe_trade_url: Url = generate_sbe_trade_url().into();
    // if let Some(trade_ip) = trade_ip {
    //     sbe_trade_url.socket_addr = Some(SocketAddr::new(
    //         IpAddr::V4(Ipv4Addr::from_str(&trade_ip).unwrap()),
    //         sbe_trade_url.port,
    //     ));
    // }
    // websocket.connect_with_headers(sbe_trade_url.clone(), TRADE_TOKEN_1, headers.clone())?;
    // websocket.connect_with_headers(sbe_trade_url.clone(), TRADE_TOKEN_2, headers.clone())?;
    // websocket.connect_with_headers(sbe_trade_url.clone(), TRADE_TOKEN_3, headers.clone())?;
    // websocket.connect_with_headers(sbe_trade_url.clone(), TRADE_TOKEN_4, headers.clone())?;

    let mut order_url: Url = generate_order_url().into();
    if let Some(oip) = order_ip {
        order_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&oip).unwrap()),
            order_url.port,
        ));
    }
    info!("order url: {}", order_url);
    websocket.connect(order_url.clone(), ORDER_TOKEN_1)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_2)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_3)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_4)?;
    // websocket.connect(REST_HOST, REST_TOKEN)?;

    // let sbe_depth_df_url: Url = generate_sbe_depth_diff_url().into();
    // websocket.connect_with_headers(sbe_depth_df_url.clone(), SBE_DEPTH_DF_1, headers.clone())?;
    // websocket.connect_with_headers(sbe_depth_df_url.clone(), SBE_DEPTH_DF_2, headers.clone())?;
    // websocket.connect_with_headers(sbe_depth_df_url.clone(), SBE_DEPTH_DF_3, headers.clone())?;
    // websocket.connect_with_headers(sbe_depth_df_url.clone(), SBE_DEPTH_DF_4, headers.clone())?;

    let mut sbe_depth_ss_url: Url = generate_sbe_depth_20_url(0).into();
    if let Some(ref depth_ip) = depth_ip {
        sbe_depth_ss_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&depth_ip).unwrap()),
            sbe_depth_ss_url.port,
        ));
    }
    info!("sbe depth ss url: {}", sbe_depth_ss_url);
    websocket.connect_with_headers(sbe_depth_ss_url.clone(), SBE_DEPTH_SS_1, headers.clone())?;
    websocket.connect_with_headers(sbe_depth_ss_url.clone(), SBE_DEPTH_SS_2, headers.clone())?;
    let mut sbe_depth_ss_url: Url = generate_sbe_depth_20_url(1).into();
    if let Some(depth_ip) = depth_ip {
        sbe_depth_ss_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&depth_ip).unwrap()),
            sbe_depth_ss_url.port,
        ));
    }
    websocket.connect_with_headers(sbe_depth_ss_url.clone(), SBE_DEPTH_SS_3, headers.clone())?;
    websocket.connect_with_headers(sbe_depth_ss_url.clone(), SBE_DEPTH_SS_4, headers.clone())?;

    // let ws_ss_url: Url = generate_ws_depth_5_url().into();
    // websocket.connect_with_headers(ws_ss_url.clone(), WS_DEPTH_SS_1, headers.clone())?;
    // websocket.connect_with_headers(ws_ss_url.clone(), WS_DEPTH_SS_2, headers.clone())?;

    // depth diff usually exceed 32KB body size, so we subscribe sbe depth diff only
    // let dp_ws_df_url: Url = generate_ws_depth_diff_url().into();
    // websocket.connect_with_headers(dp_ws_df_url.clone(), WS_DEPTH_DF_T_1, headers.clone())?;
    // websocket.connect_with_headers(dp_ws_df_url.clone(), WS_DEPTH_DF_T_2, headers.clone())?;

    // let agg_trade_url: Url = generate_ws_agg_trades_url().into();
    // websocket.connect_with_headers(agg_trade_url.clone(), WS_AGG_TRADE_T_1, headers.clone())?;
    // websocket.connect_with_headers(agg_trade_url.clone(), WS_AGG_TRADE_T_2, headers.clone())?;

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            error!("Websocket run error: {:?}", e);
            flush_logs!();
        }
    }
    Ok(())
}
