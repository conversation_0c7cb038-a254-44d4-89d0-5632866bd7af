use std::time::{SystemTime, UNIX_EPOCH};

use arrayvec::ArrayVec;
use base64::prelude::*;

use crate::{
    EdgeDirection, ORDER_FILTER_PRICE_TICK_INDEX, PREDEFINED_RINGS, TRADING_PAIR_RATES,
    TradingPair, debug,
    engine::{arbitrage_engine::ORDER_QUANTITIES, monitor::CURRENT_RING_TS},
    flush_logs,
};
use ed25519_dalek::{SigningKey, ed25519::signature::SignerMut, pkcs8::DecodePrivateKey};

use crate::{
    Currency, MAKER_FEE_INDEX, Message, ORDER_FILTER_MIN_ORDER_QTY_INDEX, ORDER_FILTERS,
    TAKER_FEE_INDEX, TRADING_FEES,
    engine::trading_pair::PRICE_TICK,
    net::{
        message::http::{HeaderMap, HttpRequest, Method},
        utils::circular_buffer::CircularBuffer,
    },
};

use super::trading_pair::LOT_SIZE_MULT;

/// 价格舍入模式
#[derive(Clone, Copy, Debug)]
pub enum RoundingMode {
    Round, // 四舍五入
    Ceil,  // 向上取整
    Floor, // 向下取整
}

/// 根据 price_tick 计算所需的小数位数
fn calculate_decimal_places(price_tick: f64) -> usize {
    if price_tick >= 1.0 {
        0
    } else {
        let mut places = 0;
        let temp = price_tick;
        while (temp * 10.0_f64.powi(places as i32)).fract().abs() > 1e-10 {
            places += 1;
            if places > 10 {
                break;
            } // 防止无限循环
        }
        places
    }
}

/// 手动格式化浮点数到字符串，避免使用 format! 宏
fn format_f64_to_string(value: f64, decimal_places: usize) -> String {
    if decimal_places == 0 {
        // 整数情况，直接使用 itoa
        let integer_part = value.round() as i64;
        return integer_part.to_string();
    }

    // 分离整数部分和小数部分
    let integer_part = value.trunc() as i64;
    let fractional_part = value.fract().abs();

    // 计算小数部分的整数表示
    let multiplier = 10.0_f64.powi(decimal_places as i32);
    let fractional_int = (fractional_part * multiplier).round() as u64;

    // 构建结果字符串
    let mut result = String::with_capacity(32);

    // 添加整数部分
    if integer_part < 0 && fractional_int > 0 {
        result.push('-');
        result.push_str(&(-integer_part).to_string());
    } else {
        result.push_str(&integer_part.to_string());
    }

    // 添加小数点和小数部分
    if fractional_int > 0 || decimal_places > 0 {
        result.push('.');

        // 添加前导零
        let fractional_str = fractional_int.to_string();
        let leading_zeros = decimal_places.saturating_sub(fractional_str.len());
        for _ in 0..leading_zeros {
            result.push('0');
        }

        // 添加小数部分，去除尾随零
        let mut fractional_chars: Vec<char> = fractional_str.chars().collect();
        while fractional_chars.len() > 1 && fractional_chars.last() == Some(&'0') {
            fractional_chars.pop();
        }
        result.extend(fractional_chars);
    }

    result
}

/// 格式化价格到指定的 price_tick 精度
fn format_price_with_tick(price: f64, price_tick: f64) -> String {
    format_price_with_tick_mode(price, price_tick, RoundingMode::Round)
}

/// 格式化价格到指定的 price_tick 精度，支持不同的舍入模式
fn format_price_with_tick_mode(price: f64, price_tick: f64, mode: RoundingMode) -> String {
    // 根据舍入模式调整价格到正确的精度
    let price_ratio = price / price_tick;
    let adjusted_price = match mode {
        RoundingMode::Round => price_ratio.round() * price_tick,
        RoundingMode::Ceil => price_ratio.ceil() * price_tick,
        RoundingMode::Floor => price_ratio.floor() * price_tick,
    };

    // 计算所需的小数位数
    let decimal_places = calculate_decimal_places(price_tick);

    // 使用优化的格式化函数
    format_f64_to_string(adjusted_price, decimal_places)
}

use crate::engine::trading_pair::{API_KEY as AK, PRIVATE_KEY_PEM as SK_PEM};

fn ed25519_sign(query: &str) -> String {
    let mut signing_key = SigningKey::from_pkcs8_pem(SK_PEM).unwrap();
    let signature = signing_key.sign(query.as_bytes());
    BASE64_STANDARD.encode(signature.to_bytes())
}

pub fn generate_trading_fee_request() -> Message<'static> {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let ts_str = timestamp.to_string();
    let params = format!("timestamp={}", ts_str);
    let signature = ed25519_sign(params.as_str());
    let query = format!("{}&signature={}", params, signature);
    let uri = format!("/sapi/v1/asset/tradeFee?{}", query);
    let mut headers = HeaderMap::new();
    headers.add("X-MBX-APIKEY", AK);
    Message::HttpRequest(HttpRequest {
        method: Method::GET,
        headers: headers,
        uri: uri.leak(),
    })
}

pub fn update_trading_fee(data: &[u8]) {
    let json: serde_json::Value = serde_json::from_slice(data).expect("Invalid JSON format");
    for item in json.as_array().unwrap() {
        let symbol = item["symbol"].as_str().unwrap();
        let maker_fee = item["makerCommission"]
            .as_str()
            .unwrap()
            .parse::<f64>()
            .unwrap();
        let taker_fee = item["takerCommission"]
            .as_str()
            .unwrap()
            .parse::<f64>()
            .unwrap();
        let pair = TradingPair::from(symbol);
        if pair == TradingPair::Unknown {
            continue;
        }
        let pair_idx = pair as usize;
        unsafe {
            TRADING_FEES[pair_idx][MAKER_FEE_INDEX] = maker_fee;
            TRADING_FEES[pair_idx][TAKER_FEE_INDEX] = taker_fee;
        }
    }
}

#[inline(always)]
fn generate_order_id<const CAP: usize>(
    json: &mut ArrayVec<u8, CAP>,
    ts: u64,
    ring_index: usize,
    edge_index: usize,
) {
    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(ts);

    let mut edge_index_buf = itoa::Buffer::new();
    let mut ring_index_buf = itoa::Buffer::new();

    let edge_index_str = match edge_index {
        0 => "0",
        1 => "1",
        2 => "2",
        3 => "3",
        _ => edge_index_buf.format(edge_index),
    };

    let ring_index_str = if ring_index < 10 {
        // 对于小于10的ring_index使用查找表
        match ring_index {
            0 => "0",
            1 => "1",
            2 => "2",
            3 => "3",
            4 => "4",
            5 => "5",
            6 => "6",
            7 => "7",
            8 => "8",
            9 => "9",
            _ => unreachable!(),
        }
    } else {
        ring_index_buf.format(ring_index)
    };

    json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
    json.push(b'-');
    json.try_extend_from_slice(ring_index_str.as_bytes())
        .unwrap();
    json.push(b'-');
    json.try_extend_from_slice(edge_index_str.as_bytes())
        .unwrap();
    json.push(b'-');
    json.push(b'0');
}

#[inline(always)]
pub fn mask_to_buf<const C: usize>(json: &[u8], now_in_ns: u64, buf: &mut CircularBuffer<C>) {
    let payload_len = json.len();
    let fin_txt = 0x81u8;
    let mut header = [0u8; 8];
    let mut header_len = 6;
    header[0] = fin_txt;
    let mut second = 0x80u8;
    match payload_len {
        len if len < 126 => {
            second |= len as u8;
        }
        len if len <= 65535 => {
            second |= 126;
            let len_bytes = (payload_len as u16).to_be_bytes();
            header[2..4].copy_from_slice(&len_bytes);
            header_len = 8;
        }
        len => {
            panic!("payload_len too long: {}", len);
        }
    }
    header[1] = second;

    let mask_u32 = now_in_ns as u32;
    let mask_bytes = mask_u32.to_be_bytes();
    match header_len {
        6 => {
            buf.put(&header[..2]).unwrap();
        }
        8 => {
            buf.put(&header[..4]).unwrap();
        }
        _ => {
            panic!("header_len: {}", header_len);
        }
    }
    buf.put(&mask_bytes[..4]).unwrap();

    for (i, byte) in json.iter().enumerate() {
        buf.put_u8(byte ^ mask_bytes[i & 3]).unwrap();
    }
}

#[inline(always)]
pub fn normlize_quantity(pair: TradingPair, edge_index: usize) -> ArrayVec<u8, 16> {
    let mutl = LOT_SIZE_MULT[pair as usize];
    let mut quantity_buf = ArrayVec::<u8, 16>::new();
    let scaled = (unsafe { ORDER_QUANTITIES[edge_index] } * mutl as f64).floor() as u64;
    let mut it = itoa::Buffer::new();
    let digits = it.format(scaled).as_bytes();
    let scale = mutl.ilog10() as usize;

    if mutl == 1 {
        quantity_buf.try_extend_from_slice(digits).unwrap();
    } else if digits.len() <= scale {
        quantity_buf.try_extend_from_slice(b"0.").unwrap();
        for _ in 0..(scale - digits.len()) {
            quantity_buf.try_extend_from_slice(b"0").unwrap();
        }
        quantity_buf.try_extend_from_slice(digits).unwrap();
    } else {
        let int_len = digits.len() - scale;
        quantity_buf
            .try_extend_from_slice(&digits[..int_len])
            .unwrap();
        quantity_buf.try_extend_from_slice(b".").unwrap();
        quantity_buf
            .try_extend_from_slice(&digits[int_len..])
            .unwrap();
    }
    quantity_buf
}

pub fn generate_cancel_order<const C: usize>(
    ring_index: usize,
    edge_index: usize,
    buf: &mut CircularBuffer<C>,
) {
    let ts = unsafe { CURRENT_RING_TS };
    let ring = PREDEFINED_RINGS[ring_index];
    let edge = ring[edge_index];
    let symbol = edge.0.to_str();
    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(ts);

    let mut json = ArrayVec::<u8, 128>::new();
    json.try_extend_from_slice(br#"{"id":""#).unwrap();
    json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"",cancel_order"#).unwrap();
    json.try_extend_from_slice(br#"","method":"order.cancel","params":{"symbol":""#)
        .unwrap();
    json.try_extend_from_slice(symbol.as_bytes()).unwrap();
    json.try_extend_from_slice(br#",origClientOrderId:""#)
        .unwrap();
    generate_order_id(&mut json, ts, ring_index, edge_index);

    mask_to_buf(json.as_slice(), ts, buf);
}

#[inline(always)]
pub fn generate_ioc_order<const C: usize>(
    ring: &[(TradingPair, EdgeDirection)],
    ring_index: usize,
    now_in_ns: u64,
    timestamp: u64,
    buf: &mut CircularBuffer<C>,
    edge_index: usize,
) {
    let mut ts_buf = itoa::Buffer::new();
    let ts_str = ts_buf.format(timestamp);
    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(now_in_ns);

    let edge = ring[edge_index];

    let quantity_buf = normlize_quantity(edge.0, edge_index);

    // 计算限价单价格
    let price = unsafe {
        match edge.1 {
            EdgeDirection::Forward => {
                1.0 / TRADING_PAIR_RATES[edge.0 as usize][EdgeDirection::Forward as usize]
            }
            EdgeDirection::Reverse => {
                TRADING_PAIR_RATES[edge.0 as usize][EdgeDirection::Reverse as usize]
            }
        }
    };

    // 获取价格精度并格式化价格
    let price_tick = ORDER_FILTERS[edge.0 as usize][ORDER_FILTER_PRICE_TICK_INDEX];
    let price_str = format_price_with_tick(price, price_tick);
    let mut price_buf = ArrayVec::<u8, 32>::new();
    price_buf
        .try_extend_from_slice(price_str.as_bytes())
        .unwrap();

    let mut json = ArrayVec::<u8, 256>::new();
    json.try_extend_from_slice(br#"{"id":""#).unwrap();
    json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"","method":"order.place","params":{"symbol":""#)
        .unwrap();
    json.try_extend_from_slice(edge.0.to_str().as_bytes())
        .unwrap();
    json.try_extend_from_slice(br#"","side":""#).unwrap();
    json.try_extend_from_slice(edge.1.to_str().as_bytes())
        .unwrap();
    json.try_extend_from_slice(br#"","timeInForce":"IOC"#)
        .unwrap();
    json.try_extend_from_slice(br#"","type":"LIMIT","newClientOrderId":""#)
        .unwrap();
    generate_order_id(&mut json, now_in_ns, ring_index, edge_index);
    json.try_extend_from_slice(br#"","price":""#).unwrap();
    json.try_extend_from_slice(price_buf.as_slice()).unwrap();
    json.try_extend_from_slice(br#"","quantity":""#).unwrap();
    json.try_extend_from_slice(quantity_buf.as_slice()).unwrap();
    json.try_extend_from_slice(br#"","timestamp":"#).unwrap();
    json.try_extend_from_slice(ts_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"}}"#).unwrap();

    mask_to_buf(json.as_slice(), now_in_ns, buf);
}

#[inline(always)]
pub fn generate_gtc_order<const C: usize>(
    ring_index: usize,
    now_in_ns: u64,
    timestamp: u64,
    buf: &mut CircularBuffer<C>,
    edge_index: usize,
    price: f64,
) {
    let mut ts_buf = itoa::Buffer::new();
    let ts_str = ts_buf.format(timestamp);
    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(now_in_ns);
    let ring = PREDEFINED_RINGS[ring_index];

    let edge = ring[edge_index];
    let quantity_buf = normlize_quantity(edge.0, edge_index);

    // 获取价格精度并格式化价格
    let price_tick = ORDER_FILTERS[edge.0 as usize][ORDER_FILTER_PRICE_TICK_INDEX];
    let price_str = format_price_with_tick(price, price_tick);
    let mut price_buf = ArrayVec::<u8, 32>::new();
    price_buf
        .try_extend_from_slice(price_str.as_bytes())
        .unwrap();

    let mut json = ArrayVec::<u8, 256>::new();
    json.try_extend_from_slice(br#"{"id":""#).unwrap();
    json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"","method":"order.place","params":{"symbol":""#)
        .unwrap();
    json.try_extend_from_slice(edge.0.to_str().as_bytes())
        .unwrap();
    json.try_extend_from_slice(br#"","side":""#).unwrap();
    json.try_extend_from_slice(edge.1.to_str().as_bytes())
        .unwrap();
    json.try_extend_from_slice(br#"","timeInForce":"GTC"#)
        .unwrap();
    json.try_extend_from_slice(br#"","type":"LIMIT","newClientOrderId":""#)
        .unwrap();
    generate_order_id(&mut json, now_in_ns, ring_index, edge_index);
    json.try_extend_from_slice(br#"","price":""#).unwrap();
    json.try_extend_from_slice(price_buf.as_slice()).unwrap();
    json.try_extend_from_slice(br#"","quantity":""#).unwrap();
    json.try_extend_from_slice(quantity_buf.as_slice()).unwrap();
    json.try_extend_from_slice(br#"","timestamp":"#).unwrap();
    json.try_extend_from_slice(ts_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"}}"#).unwrap();

    mask_to_buf(json.as_slice(), now_in_ns, buf);
}

pub fn generate_order_request_by_symbol<const N: usize>(
    now_in_ns: u64,
    buf: &mut CircularBuffer<N>,
    price: f64,
    symbol: &str,
) {
    let origin_len = buf.len();
    let price = price * 0.98;
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(now_in_ns);
    let mut ts_buf = itoa::Buffer::new();
    let ts_str = ts_buf.format(timestamp);

    let pair = TradingPair::from(symbol);

    let mut edge_index_buf = itoa::Buffer::new();
    let edge_index_str = edge_index_buf.format(0);
    let mut ring_index_buf = itoa::Buffer::new();
    let ring_index_str = ring_index_buf.format(0);
    let price_tick = PRICE_TICK[pair as usize];
    let price_str = format_price_with_tick(price, price_tick);

    let mutl = LOT_SIZE_MULT[pair as usize];
    let min_qty = ORDER_FILTERS[pair as usize][ORDER_FILTER_MIN_ORDER_QTY_INDEX];
    let quote = pair.quote();
    let quote_qty = match quote {
        Currency::XETH => 0.002f64,
        Currency::XBNB => 0.015f64,
        Currency::XBTC => 0.0008f64,
        _ => 7.0f64,
    };
    let national_qty = quote_qty / price;
    let qty = if national_qty < min_qty {
        min_qty
    } else {
        national_qty
    };
    let mut quantity_buf = CircularBuffer::<32>::new();
    let scaled = (qty * mutl as f64).floor() as u64;
    let mut it = itoa::Buffer::new();
    let digits = it.format(scaled).as_bytes();
    let scale = mutl.ilog10() as usize;

    if mutl == 1 {
        quantity_buf.put(digits).unwrap();
    } else if digits.len() <= scale {
        quantity_buf.put(b"0.").unwrap();
        for _ in 0..(scale - digits.len()) {
            quantity_buf.put(b"0").unwrap();
        }
        quantity_buf.put(digits).unwrap();
    } else {
        let int_len = digits.len() - scale;
        quantity_buf.put(&digits[..int_len]).unwrap();
        quantity_buf.put(b".").unwrap();
        quantity_buf.put(&digits[int_len..]).unwrap();
    }
    debug!(
        "symbol: {} price: {} qty: {}",
        symbol,
        price_str,
        String::from_utf8_lossy(quantity_buf.as_slices().0)
    );

    let mut json = CircularBuffer::<512>::new();
    json.put(br#"{"id":""#).unwrap();
    json.put(order_id_str.as_bytes()).unwrap();
    json.put(br#"","method":"order.place","params":{"symbol":""#)
        .unwrap();
    json.put(symbol.as_bytes()).unwrap();
    json.put(br#"","side":""#).unwrap();
    json.put(b"BUY").unwrap();
    json.put(br#"","timeInForce":"IOC"#).unwrap();
    json.put(br#"","type":"LIMIT","newClientOrderId":""#)
        .unwrap();
    json.put(order_id_str.as_bytes()).unwrap();
    json.put(b"-").unwrap();
    json.put(ring_index_str.as_bytes()).unwrap();
    json.put(b"-").unwrap();
    json.put(edge_index_str.as_bytes()).unwrap();
    json.put(b"-").unwrap();
    json.put(b"1").unwrap();
    json.put(br#"","price":""#).unwrap();
    json.put(price_str.as_bytes()).unwrap();
    json.put(br#"","quantity":""#).unwrap();
    json.put(quantity_buf.as_slices().0).unwrap();
    json.put(br#"","timestamp":"#).unwrap();
    json.put(ts_str.as_bytes()).unwrap();
    json.put(br#"}}"#).unwrap();

    let payload_len = json.len();
    let fin_txt = 0x81u8;
    let mut header = [0u8; 8];
    let mut header_len = 6;
    header[0] = fin_txt;
    let mut second = 0x80u8;
    match payload_len {
        len if len < 126 => {
            second |= len as u8;
        }
        len if len <= 65535 => {
            second |= 126;
            let len_bytes = (payload_len as u16).to_be_bytes();
            header[2..4].copy_from_slice(&len_bytes);
            header_len = 8;
        }
        len => {
            panic!("payload_len too long: {}", len);
        }
    }
    header[1] = second;

    let mask_u32 = now_in_ns as u32;
    let mask_bytes = mask_u32.to_be_bytes();
    match header_len {
        6 => match buf.put(&header[..2]) {
            Ok(_) => {}
            Err(e) => {
                crate::error!(
                    "Error writing header origin len: {}\nerr: {}",
                    origin_len,
                    e
                );
                flush_logs!();
                return;
            }
        },
        8 => match buf.put(&header[..4]) {
            Ok(_) => {}
            Err(e) => {
                crate::error!(
                    "Error writing header origin len: {}\nerr: {}",
                    origin_len,
                    e
                );
                flush_logs!();
                return;
            }
        },
        _ => {
            panic!("header_len: {}", header_len);
        }
    }
    match buf.put(&mask_bytes[..4]) {
        Ok(_) => {}
        Err(e) => {
            crate::error!("Error writing mask origin len: {}\nerr: {}", origin_len, e);
            flush_logs!();
            return;
        }
    }

    let json_len = json.len();
    for (i, byte) in json.as_slices().0.iter().enumerate() {
        match buf.put_u8(byte ^ mask_bytes[i & 3]) {
            Ok(_) => {}
            Err(e) => {
                crate::error!(
                    "Error writing json origin len: {}\njson len: {}\nerr: {}",
                    origin_len,
                    json_len,
                    e
                );
                flush_logs!();
                return;
            }
        }
    }
}

pub fn generate_user_data_sub_request() -> String {
    r#"
{
  "id": "d3df8a21-98ea-4fe0-8f4e-0fcea5d418b7",
  "method": "userDataStream.subscribe"
}
"#
    .to_string()
}

pub fn generate_session_logon_request() -> String {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let ts_str = timestamp.to_string();
    let mut params = vec![("apiKey", AK), ("timestamp", ts_str.as_str())];
    params.sort_by(|a, b| a.0.cmp(b.0));
    let param_str = params
        .iter()
        .map(|(k, v)| format!("{}={}", k, v))
        .collect::<Vec<_>>()
        .join("&");
    let signature = ed25519_sign(&param_str);
    let request = format!(
        r#"
{{
  "id": "56374a46-3061-486b-a311-99ee972eb648",
  "method": "session.logon",
  "params": {{
    "apiKey": "{}",
    "signature": "{}",
    "timestamp": {}
    }}
}}
"#,
        AK, signature, timestamp
    );
    request.to_string()
}
