use core::f64;

use crate::{
    Currency, EdgeDirection, PREDEFINED_RINGS, RING_FILLED_ORDERS, TRADING_PAIR_RATES, TradingPair,
    WebSocketHandle,
    encoding::order_update::{OrderResponse, parse_order_update},
    engine::{
        arbitrage_engine::{EXPECT_RATE, ORDER_PRICES},
        token::ORDER_TOKEN_1,
        trade::generate_gtc_order,
        trading_pair::{CURRENCY_BALANCES, RING_FILLED_PRICES},
    },
    error, error_unsafe, flush_logs, info_unsafe,
    utils::{self, perf::circles_to_ns},
};

#[derive(Debug)]
pub struct LatencyStats {
    order_latencies: [f64; 100],
    order_index: usize,
    pub win_count: usize,
    pub loss_count: usize,
}

impl LatencyStats {
    pub fn new() -> Self {
        Self {
            order_latencies: [0.0; 100],
            order_index: 0,
            win_count: 1,
            loss_count: 1,
        }
    }

    #[inline(always)]
    pub fn add_arbitrage_latency(&mut self, latency_ns: f64) {
        self.order_latencies[self.order_index] = latency_ns;
        self.order_index += 1;
    }

    pub fn print_stats(&mut self) {
        error!(
            "Wins: {} Losses: {}",
            self.win_count - 1,
            self.loss_count - 1
        );
    }
}

// 全局延迟统计
static mut LATENCY_STATS: Option<LatencyStats> = None;

pub fn init_latency_stats() {
    unsafe {
        LATENCY_STATS = Some(LatencyStats::new());
    }
}

#[inline(always)]
pub fn add_arbitrage_latency(latency_ns: f64) {
    unsafe {
        // 单线程环境，直接访问，无需任何同步开销
        if let Some(ref mut stats) = LATENCY_STATS {
            stats.add_arbitrage_latency(latency_ns);
        }
    }
}

pub fn print_latency_stats() {
    unsafe {
        if let Some(ref mut stats) = LATENCY_STATS {
            stats.print_stats();
        }
    }
}

static mut ORDER_STATUS: [u8; 10] = [0; 10]; // 1 for FILLED, 2 for others
static mut ORDER_NEW_LATENCY: [f64; 10] = [0.0; 10];
pub static mut CURRENT_RING_TS: u64 = 0;
static mut PNL: f64 = 0.0;

pub fn monitor_order_execution<const IN_LEN: usize, const OUT_LEN: usize>(
    _edge_index: usize,
    msg: &[u8],
    handle: &mut WebSocketHandle<IN_LEN, OUT_LEN>,
) -> Option<usize> {
    if let Some(order_res) = parse_order_update(msg) {
        let now = utils::perf::now();
        match order_res {
            OrderResponse::OrderUpdate(ou) => {
                let ring_index = ou.order_id.ring_index;
                let ring_len = PREDEFINED_RINGS[ring_index].len();
                let edge_index = ou.order_id.edge_index;
                let order_create_time = ou.order_id.order_create_time;
                let is_testing = ou.order_id.is_testing;
                match ou.status.as_str() {
                    "NEW" if is_testing => {
                        let latency_us = circles_to_ns(now - order_create_time) / 1000.0;
                        if latency_us > 2300.0 {
                            error!(
                                "Slow order connection: {} latency: {}",
                                ou.symbol, latency_us
                            );
                            return Some(5);
                        }
                        None
                    }
                    "EXPIRED" if !is_testing => {
                        if unsafe { ORDER_STATUS[edge_index] } != 0 {
                            return None;
                        }
                        let latency = circles_to_ns(now - order_create_time) / 1000.0;
                        error!(
                            "{} {} p: {} t: {} l: {}",
                            ou.status.as_str(),
                            ou.symbol,
                            ou.fill_price,
                            ou.order_trasaction_time,
                            latency,
                        );
                        unsafe {
                            ORDER_STATUS[edge_index] = 2;
                            let price = ORDER_PRICES[edge_index];
                            let edge = PREDEFINED_RINGS[ring_index][edge_index];
                            let new_price = match edge.1 {
                                EdgeDirection::Forward => price * (EXPECT_RATE - 0.0007),
                                EdgeDirection::Reverse => price * (2.0007 - EXPECT_RATE),
                            };
                            let timestamp = std::time::SystemTime::now()
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap()
                                .as_millis() as u64;
                            let buf = handle.get_write_buf(ORDER_TOKEN_1).unwrap();
                            generate_gtc_order(
                                ring_index,
                                CURRENT_RING_TS,
                                timestamp,
                                buf,
                                edge_index,
                                new_price,
                            );
                            handle.trigger_write(ORDER_TOKEN_1).unwrap();
                            info_unsafe!(
                                "expired order to GTC order: {} {} new price: {} old price: {}",
                                ou.symbol,
                                edge.1.to_str(),
                                new_price,
                                price
                            );
                        }
                        None
                    }
                    "REJECTED" | "CANCELED" if !is_testing => {
                        unsafe {
                            if ORDER_STATUS[edge_index] != 0 {
                                return None;
                            }
                            let latency = circles_to_ns(now - order_create_time) / 1000.0;
                            error_unsafe!(
                                "{} {} p: {} t: {} l: {}",
                                ou.status.as_str(),
                                PREDEFINED_RINGS[ring_index][edge_index].0.to_str(),
                                ou.fill_price,
                                ou.order_trasaction_time,
                                latency,
                            );
                            ORDER_NEW_LATENCY[edge_index] = latency;
                            ORDER_STATUS[edge_index] = 2;
                            RING_FILLED_ORDERS[ring_index][1] += 1;
                            if RING_FILLED_ORDERS[ring_index][1]
                                == RING_FILLED_ORDERS[ring_index][0]
                            {
                                RING_FILLED_ORDERS[ring_index][1] = 0;
                                ORDER_STATUS = [0; 10];
                                if let Some(ref mut stats) = LATENCY_STATS {
                                    stats.loss_count += 1;
                                    stats.print_stats();
                                }
                                PNL -= 0.000175 * 2.0 * 15.0; // 模拟市价单损失
                                let pnl = PNL;
                                error_unsafe!("Loss!");
                                error_unsafe!("PNL: {}", pnl);
                                let mut slowest_index = 0;
                                let mut slowest_latency = 0.0;
                                for i in 0..ring_len {
                                    if ORDER_NEW_LATENCY[i] > slowest_latency {
                                        slowest_latency = ORDER_NEW_LATENCY[i];
                                        slowest_index = i;
                                    }
                                }
                                error_unsafe!("Slowest order: {}", slowest_index);
                                error_unsafe!("Slowest latency: {:.2} us", slowest_latency);
                                flush_logs!();
                                ORDER_NEW_LATENCY = [0.0; 10];
                                if slowest_latency > 2000.0 {
                                    return Some(slowest_index);
                                } else {
                                    return None;
                                }
                            }
                        }
                        None
                    }
                    "TRADE" if !is_testing => unsafe {
                        if ou.remaining_quantity < 0.00000000001 {
                            if ou.is_maker {
                                RING_FILLED_ORDERS[ring_index][2] += 1;
                            }
                            RING_FILLED_ORDERS[ring_index][1] += 1;
                            ORDER_STATUS[edge_index] = 1;
                            error_unsafe!(
                                "Order filled count: {}",
                                RING_FILLED_ORDERS[ring_index][1]
                            );
                        }
                        match PREDEFINED_RINGS[ring_index][edge_index].1 {
                            EdgeDirection::Forward => {
                                RING_FILLED_PRICES[ring_index][edge_index] = 1.0 / ou.fill_price;
                            }
                            EdgeDirection::Reverse => {
                                RING_FILLED_PRICES[ring_index][edge_index] = ou.fill_price;
                            }
                        }
                        let latency = circles_to_ns(now - order_create_time) / 1000.0;
                        error_unsafe!(
                            "{} {} p: {} t: {} l: {:.1} is_maker: {}",
                            ou.status.as_str(),
                            PREDEFINED_RINGS[ring_index][edge_index].0.to_str(),
                            ou.fill_price,
                            ou.order_trasaction_time,
                            latency,
                            ou.is_maker,
                        );
                        ORDER_NEW_LATENCY[edge_index] = latency;
                        if RING_FILLED_ORDERS[ring_index][1] == RING_FILLED_ORDERS[ring_index][0] {
                            RING_FILLED_ORDERS[ring_index][1] = 0;
                            let maker_count = RING_FILLED_ORDERS[ring_index][2] as f64;
                            let taker_count = ring_len - maker_count as usize;
                            let expect = 1.0
                                + taker_count as f64 * 0.000175
                                + maker_count as f64 * 0.0000875;
                            RING_FILLED_ORDERS[ring_index][2] = 0;
                            let mut real = 1.0f64;
                            let mut status_sum = 0;
                            for i in 0..ring_len {
                                real *= RING_FILLED_PRICES[ring_index][i];
                                status_sum += ORDER_STATUS[i] as usize;
                            }
                            if status_sum == ring_len {
                                error_unsafe!("Real: {:.6} vs Expect: {:.6}", real, expect);
                                if (real - expect) < -0.0000001 {
                                    PNL -= (expect - real) * 15.0;
                                    if let Some(ref mut stats) = LATENCY_STATS {
                                        stats.loss_count += 1;
                                        error_unsafe!("Loss!");
                                        stats.print_stats();
                                    }
                                } else {
                                    PNL += (real - expect) * 15.0;
                                    if let Some(ref mut stats) = LATENCY_STATS {
                                        stats.win_count += 1;
                                        error_unsafe!("Win!");
                                        stats.print_stats();
                                    }
                                }
                            } else {
                                PNL -= 0.000175 * 2.0 * 15.0;
                                if let Some(ref mut stats) = LATENCY_STATS {
                                    stats.loss_count += 1;
                                    error_unsafe!("Loss!");
                                    stats.print_stats();
                                }
                            }
                            let pnl = PNL;
                            error_unsafe!("PNL: {}", pnl);
                            ORDER_STATUS = [0; 10];
                            let mut slowest_index = 0;
                            let mut slowest_latency = 0.0;
                            for i in 0..ring_len {
                                if ORDER_NEW_LATENCY[i] > slowest_latency {
                                    slowest_latency = ORDER_NEW_LATENCY[i];
                                    slowest_index = i;
                                }
                            }
                            error_unsafe!("Slowest order: {}", slowest_index);
                            error_unsafe!("Slowest latency: {:.2} us", slowest_latency);
                            flush_logs!();
                            ORDER_NEW_LATENCY = [0.0; 10];
                            if slowest_latency > 2000.0 {
                                return Some(slowest_index);
                            } else {
                                return None;
                            }
                        }
                        None
                    },
                    _ => None,
                }
            }
            OrderResponse::OrderCreated(_ou) => None,
            OrderResponse::AccountPosition(account_pos) => unsafe {
                for balance in account_pos.balances {
                    let price = match balance.asset {
                        Currency::XUSDT => 1.0,
                        _ => {
                            let pair = TradingPair::from((balance.asset, Currency::XUSDT));
                            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize]
                        }
                    };
                    CURRENCY_BALANCES[balance.asset as usize] = balance.free * price;
                }
                None
            },
            OrderResponse::OrderError(_) => None,
        }
    } else {
        None
    }
}
