use crate::utils;

pub mod circular_buffer;
pub mod url;

const BASE64: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

#[inline]
pub fn generate_key() -> String {
    let now = utils::perf::now();
    let key: [u8; 16] = [
        (now >> 56) as u8,
        (now >> 48) as u8,
        (now >> 40) as u8,
        (now >> 32) as u8,
        (now >> 24) as u8,
        (now >> 16) as u8,
        (now >> 8) as u8,
        (now >> 0) as u8,
        (now >> 56) as u8,
        (now >> 48) as u8,
        (now >> 40) as u8,
        (now >> 32) as u8,
        (now >> 24) as u8,
        (now >> 16) as u8,
        (now >> 8) as u8,
        (now >> 0) as u8,
    ];
    encode_base64(&key)
}

#[inline]
pub fn encode_base64(data: &[u8]) -> String {
    let len = data.len();
    let mod_len = len % 3;

    let mut encoded = vec![b'='; (len + 2) / 3 * 4];
    {
        let mut in_iter = data[..len - mod_len].iter().map(|&c| u32::from(c));
        let mut out_iter = encoded.iter_mut();

        let enc = |val| BASE64[val as usize];
        let mut write = |val| *out_iter.next().unwrap() = val;

        while let (Some(one), Some(two), Some(three)) =
            (in_iter.next(), in_iter.next(), in_iter.next())
        {
            let g24 = one << 16 | two << 8 | three;
            write(enc((g24 >> 18) & 63));
            write(enc((g24 >> 12) & 63));
            write(enc((g24 >> 6) & 63));
            write(enc(g24 & 63));
        }

        match mod_len {
            1 => {
                let pad = (u32::from(data[len - 1])) << 16;
                write(enc((pad >> 18) & 63));
                write(enc((pad >> 12) & 63));
            }
            2 => {
                let pad = (u32::from(data[len - 2])) << 16 | (u32::from(data[len - 1])) << 8;
                write(enc((pad >> 18) & 63));
                write(enc((pad >> 12) & 63));
                write(enc((pad >> 6) & 63));
            }
            _ => (),
        }
    }

    String::from_utf8(encoded).unwrap()
}
