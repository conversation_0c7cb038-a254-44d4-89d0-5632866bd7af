use std::time::Duration;

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Settings {
    /// The maximum number of fragments the connection can handle without reallocating.
    /// Default: 10
    pub fragments_capacity: usize,
    /// Whether to reallocate when `fragments_capacity` is reached. If this is false,
    /// a Capacity error will be triggered instead.
    /// Default: true
    pub fragments_grow: bool,
    /// The maximum length of outgoing frames. Messages longer than this will be fragmented.
    /// Default: 65,535
    pub fragment_size: usize,
    /// The maximum length of acceptable incoming frames. Messages longer than this will be rejected.
    /// Default: unlimited
    pub max_fragment_size: usize,
    pub shutdown_on_interrupt: bool,
    /// The WebSocket protocol requires frames sent from client endpoints to be masked as a
    /// security and sanity precaution. Enforcing this requirement, which may be removed at some
    /// point may cause incompatibilities. If you need the extra security, set this to true.
    /// Default: false
    pub masking_strict: bool,
    /// The WebSocket protocol requires clients to verify the key returned by a server to ensure
    /// that the server and all intermediaries can perform the protocol. Verifying the key will
    /// consume processing time and other resources with the benefit that we can fail the
    /// connection early. The default in WS-RS is to accept any key from the server and instead
    /// fail late if a protocol error occurs. Change this setting to enable key verification.
    /// Default: false
    pub key_strict: bool,
    /// The WebSocket protocol requires clients to perform an opening handshake using the HTTP
    /// GET method for the request. However, since only WebSockets are supported on the connection,
    /// verifying the method of handshake requests is not always necessary. To enforce the
    /// requirement that handshakes begin with a GET method, set this to true.
    /// Default: false
    pub method_strict: bool,
    /// Disables Nagle's algorithm.
    /// Usually tcp socket tries to accumulate packets to send them all together (every 200ms).
    /// When enabled socket will try to send packet as fast as possible.
    ///
    /// Default: true
    pub tcp_nodelay: bool,
    /// the event loop will block on poll if the event_loop_timeout is None
    ///
    /// Default: 100us
    pub event_loop_timeout: Option<Duration>,
    /// whether auto reconnect when connection is closed
    /// when use http connection, this option will reduce a lot http request ttl
    ///
    /// Default: false
    pub auto_reconnect: bool,
    /// whether stop the loop when no connection
    ///
    /// Default: true
    pub stop_when_no_connection: bool,

    pub test_cert_path: String,

    pub socket_buffer_size: usize,
}

impl Default for Settings {
    fn default() -> Settings {
        Settings {
            fragments_capacity: 100,
            fragments_grow: true,
            fragment_size: u16::MAX as usize,
            max_fragment_size: usize::MAX,
            shutdown_on_interrupt: true,
            masking_strict: false,
            key_strict: false,
            method_strict: false,
            tcp_nodelay: true,
            event_loop_timeout: Some(Duration::new(1, 0)),
            auto_reconnect: true,
            stop_when_no_connection: true,
            test_cert_path: "".to_string(),
            socket_buffer_size: 1024 * 4,
        }
    }
}
