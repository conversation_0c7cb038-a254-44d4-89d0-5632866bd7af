use core::fmt;
use std::borrow::Cow;
use std::default::Default;

use super::opcode::OpCode;
use crate::net::CloseCode;
use crate::net::result::{Error, Kind, Result};
use crate::net::utils::circular_buffer::CircularBuffer;
use crate::utils::perf::now;

fn apply_mask(buf: &mut [u8], mask: &[u8; 4]) {
    let iter = buf.iter_mut().zip(mask.iter().cycle());
    for (byte, &key) in iter {
        *byte ^= key
    }
}

/// Handling of the length format.
enum LengthFormat {
    U8,
    U16,
    U64,
}

impl LengthFormat {
    /// Get the size of the length encoding.
    #[inline]
    fn extra_bytes(&self) -> usize {
        match *self {
            LengthFormat::U8 => 0,
            LengthFormat::U16 => 2,
            LengthFormat::U64 => 8,
        }
    }

    fn for_byte(byte: u8) -> Self {
        match byte & 0x7F {
            126 => LengthFormat::U16,
            127 => LengthFormat::U64,
            _ => LengthFormat::U8,
        }
    }
}

pub struct Frame<'f> {
    finished: bool,
    rsv1: bool,
    rsv2: bool,
    rsv3: bool,
    opcode: OpCode,
    mask: Option<[u8; 4]>,
    payload_len: usize,
    pub payload: Cow<'f, [u8]>,
}

impl<'f> Frame<'f> {
    #[inline]
    pub fn is_final(&self) -> bool {
        self.finished
    }

    #[inline]
    pub fn has_rsv1(&self) -> bool {
        self.rsv1
    }

    #[inline]
    pub fn has_rsv2(&self) -> bool {
        self.rsv2
    }

    #[inline]
    pub fn has_rsv3(&self) -> bool {
        self.rsv3
    }

    #[inline]
    pub fn opcode(&self) -> OpCode {
        self.opcode
    }

    #[inline]
    pub fn is_control(&self) -> bool {
        self.opcode.is_control()
    }

    #[inline]
    pub fn is_masked(&self) -> bool {
        self.mask.is_some()
    }

    #[allow(dead_code)]
    #[inline]
    pub fn mask(&self) -> Option<&[u8; 4]> {
        self.mask.as_ref()
    }

    /// Set the first reserved bit.
    #[inline]
    pub fn set_rsv1(&mut self, has_rsv1: bool) -> &mut Frame<'f> {
        self.rsv1 = has_rsv1;
        self
    }

    /// Set the second reserved bit.
    #[inline]
    pub fn set_rsv2(&mut self, has_rsv2: bool) -> &mut Frame<'f> {
        self.rsv2 = has_rsv2;
        self
    }

    /// Set the third reserved bit.
    #[inline]
    pub fn set_rsv3(&mut self, has_rsv3: bool) -> &mut Frame<'f> {
        self.rsv3 = has_rsv3;
        self
    }

    /// Set the OpCode.
    #[allow(dead_code)]
    #[inline]
    pub fn set_opcode(&mut self, opcode: OpCode) -> &mut Frame<'f> {
        self.opcode = opcode;
        self
    }

    #[inline(always)]
    pub fn set_mask(&mut self) -> &mut Frame<'f> {
        let now = now() as u32;
        self.mask = Some(now.to_le_bytes());
        self
    }

    #[inline]
    pub fn remove_mask(&mut self) -> &mut Frame<'f> {
        if let Some(mask) = self.mask.take() {
            let mut payload = self.payload.to_vec();
            apply_mask(&mut payload, &mask);
            self.payload = payload.into();
        }
        self
    }

    #[inline]
    pub fn message<D>(data: D, code: OpCode, finished: bool) -> Frame<'f>
    where
        D: Into<Cow<'f, [u8]>>,
    {
        Frame {
            finished,
            opcode: code,
            payload: data.into(),
            ..Frame::default()
        }
    }

    /// Create a new Pong control frame.
    #[inline]
    pub fn pong(data: Vec<u8>) -> Frame<'f> {
        Frame {
            opcode: OpCode::Pong,
            payload: data.into(),
            ..Frame::default()
        }
    }

    /// Create a new Ping control frame.
    #[inline]
    pub fn ping(data: Vec<u8>) -> Frame<'f> {
        Frame {
            opcode: OpCode::Ping,
            payload: data.into(),
            ..Frame::default()
        }
    }

    /// Create a new Close control frame.
    #[inline]
    pub fn close(code: CloseCode, reason: &str) -> Frame<'_> {
        let payload = if let CloseCode::Empty = code {
            Vec::new()
        } else {
            let u: u16 = code.into();
            let raw = [(u >> 8) as u8, u as u8];
            [&raw, reason.as_bytes()].concat()
        };

        Frame {
            payload: payload.into(),
            ..Frame::default()
        }
    }

    // #[measure]
    pub fn parse<'a, const N: usize>(
        buffer: &mut CircularBuffer<N>,
        max_payload_length: usize,
    ) -> Result<Option<Frame<'a>>> {
        let first = match buffer.peek() {
            Some(byte) => byte,
            None => {
                buffer.reset_read();
                return Ok(None);
            }
        };
        buffer.advance_read(1);
        let second = match buffer.peek() {
            Some(byte) => byte,
            None => {
                buffer.reset_read();
                return Ok(None);
            }
        };
        buffer.advance_read(1);
        let finished = first & 0x80 != 0;
        let rsv1 = first & 0x40 != 0;
        let rsv2 = first & 0x20 != 0;
        let rsv3 = first & 0x10 != 0;
        if rsv1 || rsv2 || rsv3 {
            buffer.reset_read();
            return Err(Error::new(Kind::Protocol, "rsv value must be 0"));
        }

        let opcode = OpCode::from(first & 0x0F);
        let masked = second & 0x80 != 0;
        let length = {
            let length_nbytes = LengthFormat::for_byte(second).extra_bytes();
            match length_nbytes {
                2 => match buffer.peek_u16_be() {
                    Some(len) => {
                        buffer.advance_read(2);
                        len as u64
                    }
                    None => {
                        buffer.reset_read();
                        return Ok(None);
                    }
                },
                8 => match buffer.peek_u64_be() {
                    Some(len) => {
                        buffer.advance_read(8);
                        len
                    }
                    None => {
                        buffer.reset_read();
                        return Ok(None);
                    }
                },
                _ => u64::from(second & 0x7F),
            }
        };
        if length > max_payload_length as u64 {
            return Err(Error::new(
                Kind::Protocol,
                format!(
                    "Rejected frame with payload length exceeding defined max: {}.",
                    max_payload_length
                ),
            ));
        }
        if let OpCode::Bad = opcode {
            return Err(Error::new(
                Kind::Protocol,
                format!("Encountered invalid opcode: {}", first & 0x0F),
            ));
        }

        // control frames must have length <= 125
        match opcode {
            OpCode::Ping | OpCode::Pong if length > 125 => {
                return Err(Error::new(
                    Kind::Protocol,
                    format!(
                        "Rejected WebSocket handshake.Received control frame with length: {}.",
                        length
                    ),
                ));
            }
            OpCode::Close if length > 125 => {
                return Err(Error::new(
                    Kind::Protocol,
                    "Received close frame with payload length than 125.",
                ));
            }
            OpCode::Close if length < 2 && length != 0 => {
                return Err(Error::new(
                    Kind::Protocol,
                    "Received close frame with payload length less than 2.",
                ));
            }
            _ => (),
        }
        let mask = if masked {
            if buffer.pending_len() < 4 {
                buffer.reset_read();
                return Ok(None);
            }
            let mut mask = [0u8; 4];
            buffer.read_into(&mut mask);
            Some(mask)
        } else {
            None
        };

        if buffer.pending_len() < length as usize {
            buffer.reset_read();
            return Ok(None);
        }
        let (slice1, slice2) = buffer.as_slices();
        let payload: Cow<'_, [u8]> = if slice1.len() >= length as usize {
            unsafe {
                let base = slice1.as_ptr().cast::<u8>();
                let a = core::slice::from_raw_parts(base, length as usize);
                a.into()
            }
        } else {
            let mut payload = vec![0u8; length as usize];
            payload[..slice1.len()].copy_from_slice(slice1);
            payload[slice1.len()..length as usize]
                .copy_from_slice(&slice2[..length as usize - slice1.len()]);
            payload.into()
        };
        buffer.advance_and_commit_read(length as usize);
        let mut frame = Frame {
            finished,
            rsv1,
            rsv2,
            rsv3,
            opcode,
            mask,
            payload_len: length as usize,
            payload,
        };
        frame.remove_mask();
        Ok(Some(frame))
    }

    /// Write a frame out to a buffer
    pub fn format<const N: usize>(&mut self, w: &mut CircularBuffer<N>) -> Result<()> {
        let mut one = 0u8;
        let opcode: u8 = self.opcode.into();
        if self.is_final() {
            one |= 0x80;
        }
        if self.has_rsv1() {
            one |= 0x40;
        }
        if self.has_rsv2() {
            one |= 0x20;
        }
        if self.has_rsv3() {
            one |= 0x10;
        }
        one |= opcode;
        w.put_u8(one).unwrap();

        let mut two = 0u8;
        if self.is_masked() {
            two |= 0x80;
        }

        match self.payload.len() {
            len if len < 126 => {
                two |= len as u8;
                w.put_u8(two).unwrap();
            }
            len if len <= 65535 => {
                two |= 126;
                w.put_u8(two).unwrap();
                w.put_u16(len as u16).unwrap();
            }
            len => {
                two |= 127;
                w.put_u8(two).unwrap();
                w.put_u64(len as u64).unwrap();
            }
        }

        if self.is_masked() {
            let mask = self.mask.take().unwrap();
            w.put(mask).unwrap();
            let mut i = 0;
            let mask_u32 = u32::from_be_bytes(mask.try_into().unwrap());
            while i + 4 <= self.payload.len() {
                let chunk = u32::from_be_bytes(self.payload[i..i + 4].try_into().unwrap());
                let masked = chunk ^ mask_u32;
                w.put_u32(masked).unwrap();
                i += 4;
            }
            for &b in &self.payload[i..] {
                w.put_u8(b ^ mask[i % 4]).unwrap();
                i += 1;
            }
        } else {
            w.put(&self.payload).unwrap();
        }

        Ok(())
    }
}

impl Default for Frame<'_> {
    fn default() -> Frame<'static> {
        Frame {
            finished: true,
            rsv1: false,
            rsv2: false,
            rsv3: false,
            opcode: OpCode::Close,
            mask: None,
            payload_len: 0,
            payload: Cow::Borrowed(&[]),
        }
    }
}

impl fmt::Display for Frame<'_> {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(
            f,
            r#"
<FRAME>
final: {}
reserved: {} {} {}
opcode: {}
payload length: {}
payload: {:?}"#,
            self.finished,
            self.rsv1,
            self.rsv2,
            self.rsv3,
            self.opcode,
            self.payload_len,
            self.payload,
        )
    }
}

impl fmt::Debug for Frame<'_> {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(
            f,
            r#"
<FRAME>
final: {}
reserved: {} {} {}
opcode: {}
payload length: {}
payload: {:?}"#,
            self.finished,
            self.rsv1,
            self.rsv2,
            self.rsv3,
            self.opcode,
            self.payload_len,
            self.payload,
        )
    }
}

#[cfg(test)]
mod test {
    #![allow(unused_imports, unused_variables, dead_code)]
    use std::io::Read;

    use super::*;
    use crate::net::OpCode;

    #[test]
    fn display_frame() {
        let f = Frame::message("hi there".as_bytes(), OpCode::Text, true);
        let view = format!("{}", f);
        assert!(view.contains("payload:"));
    }

    #[test]
    fn test_parse_empty_buffer() {
        let mut bytes = CircularBuffer::<128>::new();
        let result = super::Frame::parse(&mut bytes, 1024);
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());
    }

    #[test]
    fn test_parse_invalid_rsv() {
        let buffer = vec![0b1111_1111u8, 0b0000_0000u8]; // Invalid RSV
        let mut bytes = CircularBuffer::<128>::from(buffer);
        let result = super::Frame::parse(&mut bytes, 1024);
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_exceeds_max_payload_length() {
        let buffer = vec![0b1000_0001u8, 0b0000_0010u8]; // Finished frame with length 2
        let payload = vec![1u8, 2u8]; // Payload of length 2
        let buffer = [buffer, payload].concat();
        let mut bytes = CircularBuffer::<128>::from(buffer);
        let result = super::Frame::parse(&mut bytes, 1); // Max payload length set to 1
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_valid_frame() {
        let buffer = vec![0b1000_0001u8, 0b0000_0010u8]; // Finished frame with length 2
        let payload = vec![1, 2]; // Payload of length 2
        let buffer = [buffer, payload].concat();
        let mut bytes = CircularBuffer::<128>::from(buffer);
        let result = super::Frame::parse(&mut bytes, 1024);
        assert!(result.is_ok());
        let frame = result.unwrap().unwrap();
        assert!(frame.finished);
        assert_eq!(frame.payload.as_ref(), vec![1, 2]);
    }

    #[test]
    fn test_parse_masked_frame() {
        let buffer = vec![0b1000_0001u8, 0b1000_0010u8]; // Finished, masked frame
        let payload = vec![1u8, 2u8]; // Payload of length 2
        let mask = vec![0u8, 1u8, 2u8, 3u8]; // Mask
        let buffer = [buffer, mask.clone(), payload].concat();
        let mut bytes = CircularBuffer::<128>::from(buffer);
        let result = super::Frame::parse(&mut bytes, 1024);
        assert!(result.is_ok());
        let frame = result.unwrap().unwrap();
        let mut payload = vec![1u8, 2u8]; // Payload of length 2
        apply_mask(&mut payload, &[0u8, 1u8, 2u8, 3u8]);
        assert_eq!(frame.payload.as_ref(), payload);
    }

    #[test]
    fn test_parse_control_frame_length() {
        let buffer = vec![0b1000_1000u8, 0b0111_1110u8, 0b0000_0000u8, 0b1111_1110u8]; // Ping opcode
        let payload = vec![1u8, 2u8, 3u8]; // Payload of length 3
        let buffer = [buffer, payload].concat();
        let mut bytes = CircularBuffer::<128>::from(buffer);
        let result = super::Frame::parse(&mut bytes, 1024);
        assert!(result.is_err()); // Control frames should not exceed length 125
    }

    #[test]
    fn test_parse_close_frame_length() {
        let buffer = vec![0b1000_1000u8, 0b0000_0010u8]; // Close frame
        let payload = vec![b'a', b'b']; // Payload of length 1
        let buffer = [buffer, payload].concat();
        let mut bytes = CircularBuffer::<128>::from(buffer);
        let result = super::Frame::parse(&mut bytes, 1024);
        assert!(result.is_ok()); // Close frame length must be 2 or 0
    }

    #[test]
    fn test_format_final_frame_no_mask_no_extension() {
        let mut frame = super::Frame {
            opcode: OpCode::Text, // Assuming OpCode::Text is defined
            payload: "Hello".as_bytes().into(),
            mask: None,
            finished: true,
            payload_len: 5,
            rsv1: false,
            rsv2: false,
            rsv3: false,
        };

        let mut buffer = CircularBuffer::<128>::new();
        let result = frame.format(&mut buffer);

        assert!(result.is_ok());
        assert_eq!(buffer.len(), 2 + frame.payload.len());
        assert_eq!(buffer.peek().unwrap(), 0b_10000001u8); // FIN + Text frame opcode
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek().unwrap(), 5); // Length of payload
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.as_slices().0, b"Hello"); // Check payload
    }

    #[test]
    fn test_format_final_frame_no_mask_no_extension_150_payload() {
        let mut frame = super::Frame {
            opcode: OpCode::Text, // Assuming OpCode::Text is defined
            payload: "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111".as_bytes().into(),
            mask: None,
            finished: true,
            payload_len: 150,
            rsv1: false,
            rsv2: false,
            rsv3: false,
        };

        let mut buffer = CircularBuffer::<1024>::new();
        let result = frame.format(&mut buffer);

        assert!(result.is_ok());
        assert_eq!(buffer.len(), 2 + 2 + frame.payload.len());
        assert_eq!(buffer.peek().unwrap(), 0b_10000001u8); // FIN + Text frame opcode
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek().unwrap(), 126); // Length of payload
        buffer.advance_and_commit_read(1);
        let lenth = buffer.peek_u16_be().unwrap();
        assert_eq!(lenth as usize, frame.payload.len()); // Length of payload
        buffer.advance_and_commit_read(2);
        assert_eq!(
            buffer.as_slices().0,
            b"111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
        ); // Check payload
    }

    #[test]
    fn test_format_non_final_frame_with_mask() {
        let mut payload = b"Hello".to_vec();
        let mask = [0x1, 0x2, 0x3, 0x4]; // Example mask
        let mut frame = super::Frame {
            opcode: OpCode::Binary, // Assuming OpCode::Binary is defined
            payload: payload.clone().into(),
            mask: Some(mask.clone()), // Example mask
            finished: false,
            payload_len: 5,
            rsv1: false,
            rsv2: false,
            rsv3: false,
        };

        let mut buffer = CircularBuffer::<128>::new();
        let result = frame.format(&mut buffer);

        assert!(result.is_ok());
        assert_eq!(buffer.len(), 2 + frame.payload.len() + 4); // Include mask
        assert_eq!(buffer.peek().unwrap(), 0b_00000010u8); // Non-FIN + Binary frame opcode
        buffer.advance_and_commit_read(1);
        assert_eq!(buffer.peek().unwrap(), 5 | 0x80); // Length with mask (0x80 bit set)
        buffer.advance_and_commit_read(1);

        // Check masked payload
        for (i, byte) in payload.iter_mut().enumerate() {
            *byte ^= mask[i % 4]; // Apply the mask using XOR
        }
        buffer.advance_and_commit_read(4);
        assert_eq!(buffer.as_slices().0, payload); // Check masked payload
    }
}
