use crate::flush_logs;

use super::Message;
use super::Settings;
use super::connection::Connection;
use super::result::{<PERSON>rror, Kind, Result};
use super::utils::circular_buffer::CircularBuffer;
use super::utils::url::Url;
use core::fmt;
use mio;
use mio::Events;
use mio::{Interest, Poll, Token};

const MAX_EVENTS: usize = 1024;
// 优化：使用数组替代HashMap的最大连接数
const MAX_CONNECTIONS: usize = 100;

#[derive(Debug)]
enum State {
    Active,
    Inactive,
}

impl State {
    fn is_active(&self) -> bool {
        match *self {
            State::Active => true,
            State::Inactive => false,
        }
    }
}

pub enum CallbackData {
    ConnectionOpen(Token),
    Message(Token, Message<'static>),
    ConnectionClose(Token, Error),
    ConnectionError(Token, Error),
}

impl fmt::Debug for CallbackData {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            CallbackData::ConnectionOpen(token) => {
                write!(f, "CallbackData::ConnectionOpen({:?})", token)
            }
            CallbackData::Message(token, msg) => {
                write!(f, "CallbackData::Message({:?}, {:?})", token, msg)
            }
            CallbackData::ConnectionClose(token, err) => {
                write!(f, "CallbackData::ConnectionClose({:?} {:?})", token, err)
            }
            CallbackData::ConnectionError(token, err) => {
                write!(f, "CallbackData::ConnectionError({:?}, {:?})", token, err)
            }
        }
    }
}

pub struct WebSocketHandle<const IN_LEN: usize, const OUT_LEN: usize> {
    connections: [Option<Connection<IN_LEN, OUT_LEN>>; MAX_CONNECTIONS],
    poll: Poll,
    state: State,
}

impl<const IN_LEN: usize, const OUT_LEN: usize> WebSocketHandle<IN_LEN, OUT_LEN> {
    // 优化：使用数组直接访问，避免HashMap查找
    #[inline(always)]
    fn get_connection_mut(&mut self, token: Token) -> Option<&mut Connection<IN_LEN, OUT_LEN>> {
        let index = token.0;
        if index < MAX_CONNECTIONS {
            self.connections[index].as_mut()
        } else {
            None // 超出范围的token直接返回None
        }
    }

    // 插入连接到数组
    #[inline]
    fn insert_connection(&mut self, token: Token, conn: Connection<IN_LEN, OUT_LEN>) -> Result<()> {
        let index = token.0;
        if index < MAX_CONNECTIONS {
            self.connections[index] = Some(conn);
            Ok(())
        } else {
            Err(Error::new(
                Kind::Internal,
                format!(
                    "Token index {} exceeds maximum connections {}",
                    index, MAX_CONNECTIONS
                ),
            ))
        }
    }

    // 检查是否所有连接都为空
    #[inline]
    fn is_connections_empty(&self) -> bool {
        self.connections.iter().all(|conn| conn.is_none())
    }

    pub fn send_message<M>(&mut self, token: Token, msg: M) -> Result<()>
    where
        M: Into<Message<'static>>,
    {
        let msg = msg.into();
        let conn = self.get_connection_mut(token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.write_message(msg)?;
        Ok(())
    }

    #[inline]
    pub fn get_write_buf(&mut self, token: Token) -> Result<&mut CircularBuffer<OUT_LEN>> {
        let conn = self.get_connection_mut(token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        Ok(conn.write_buf_mut())
    }

    #[inline]
    pub fn trigger_write(&mut self, token: Token) -> Result<bool> {
        let conn = self.get_connection_mut(token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        match conn.write() {
            Ok(true) => Ok(true),
            Ok(false) => {
                crate::error!("not written all reregister_writable: {:?}", token);
                self.reregister_writable(token)?;
                Ok(false)
            }
            Err(err) => Err(err),
        }
    }

    // 优化版本：批量触发写入，使用数组直接访问
    #[inline]
    pub fn trigger_write_batch(&mut self, tokens: &[Token]) -> Result<()> {
        for &token in tokens {
            if let Some(conn) = self.get_connection_mut(token) {
                conn.write()?;
            }
        }
        Ok(())
    }

    // 针对固定数量token的特化版本，使用数组直接访问
    #[inline]
    pub fn trigger_write_3(&mut self, token1: Token, token2: Token, token3: Token) -> Result<()> {
        if let Some(conn) = self.get_connection_mut(token1) {
            match conn.write() {
                Ok(true) => {}
                Ok(false) => {
                    self.reregister_writable(token1)?;
                }
                Err(err) => return Err(err),
            }
        }
        if let Some(conn) = self.get_connection_mut(token2) {
            match conn.write() {
                Ok(true) => {}
                Ok(false) => {
                    self.reregister_writable(token2)?;
                }
                Err(err) => return Err(err),
            }
        }
        if let Some(conn) = self.get_connection_mut(token3) {
            match conn.write() {
                Ok(true) => {}
                Ok(false) => {
                    self.reregister_writable(token3)?;
                }
                Err(err) => return Err(err),
            }
        }
        Ok(())
    }

    #[inline]
    pub fn trigger_write_4(
        &mut self,
        token1: Token,
        token2: Token,
        token3: Token,
        token4: Token,
    ) -> Result<()> {
        if let Some(conn) = self.get_connection_mut(token1) {
            match conn.write() {
                Ok(true) => {}
                Ok(false) => {
                    self.reregister_writable(token1)?;
                }
                Err(err) => return Err(err),
            }
        }
        if let Some(conn) = self.get_connection_mut(token2) {
            match conn.write() {
                Ok(true) => {}
                Ok(false) => {
                    self.reregister_writable(token2)?;
                }
                Err(err) => return Err(err),
            }
        }
        if let Some(conn) = self.get_connection_mut(token3) {
            match conn.write() {
                Ok(true) => {}
                Ok(false) => {
                    self.reregister_writable(token3)?;
                }
                Err(err) => return Err(err),
            }
        }
        if let Some(conn) = self.get_connection_mut(token4) {
            match conn.write() {
                Ok(true) => {}
                Ok(false) => {
                    self.reregister_writable(token4)?;
                }
                Err(err) => return Err(err),
            }
        }
        Ok(())
    }

    #[inline]
    pub fn send_messages<M>(&mut self, token: Token, msgs: Vec<M>) -> Result<()>
    where
        M: Into<Message<'static>>,
    {
        let conn = self.get_connection_mut(token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.send_messages(msgs)?;
        Ok(())
    }

    pub fn send_message_n<const NUM: usize, M>(
        &mut self,
        token: Token,
        msgs: [M; NUM],
    ) -> Result<()>
    where
        M: Into<Message<'static>>,
    {
        let conn = self.get_connection_mut(token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.write_message_n(msgs)?;
        Ok(())
    }

    pub fn close_conn(&mut self, token: Token) -> Result<()> {
        let conn = self.get_connection_mut(token).ok_or_else(|| {
            Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            )
        })?;
        conn.shutdown()?;
        Ok(())
    }

    pub fn reconnect_connection(&mut self, token: Token) -> Result<()> {
        let socket = self.connections[token.0].as_mut().unwrap().socket_mut();
        self.poll.registry().deregister(socket)?;
        let new_conn = self.connections[token.0].as_mut().unwrap().reconnect()?;
        self.insert_connection(token, new_conn)?;
        let socket_mut = self.connections[token.0].as_mut().unwrap().socket_mut();
        self.poll
            .registry()
            .register(socket_mut, token, Interest::READABLE | Interest::WRITABLE)
            .map_err(|err| {
                Error::new(
                    Kind::Internal,
                    format!(
                        "Encountered error when trying to build WebSocket connection {}",
                        err
                    ),
                )
            })
    }

    pub fn stop(&mut self) {
        self.state = State::Inactive;
    }

    pub fn reregister_readable(&mut self, token: Token) -> Result<()> {
        let index = token.0;
        if index >= MAX_CONNECTIONS {
            return Err(Error::new(
                Kind::Internal,
                format!(
                    "Token index {} exceeds maximum connections {}",
                    index, MAX_CONNECTIONS
                ),
            ));
        }

        // Check if connection exists
        if self.connections[index].is_none() {
            return Err(Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            ));
        }

        // Split the borrow: get the socket reference first
        let socket = self.connections[index].as_mut().unwrap().socket_mut();

        // Now we can access self.poll
        self.poll
            .registry()
            .reregister(socket, token, Interest::READABLE)
            .map_err(|err| {
                Error::new(
                    Kind::Internal,
                    format!(
                        "Encountered error when trying to reregister WebSocket connection {} token: {:?}",
                        err, token,
                    ),
                )
            })
    }

    pub fn reregister_writable(&mut self, token: Token) -> Result<()> {
        let index = token.0;
        if index >= MAX_CONNECTIONS {
            return Err(Error::new(
                Kind::Internal,
                format!(
                    "Token index {} exceeds maximum connections {}",
                    index, MAX_CONNECTIONS
                ),
            ));
        }

        // Check if connection exists
        if self.connections[index].is_none() {
            return Err(Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            ));
        }

        // Split the borrow: get the socket reference first
        let socket = self.connections[index].as_mut().unwrap().socket_mut();

        // Now we can access self.poll
        crate::info!("reregister_writable: {:?}", token);
        flush_logs!();
        self.poll
            .registry()
            .reregister(socket, token, Interest::READABLE | Interest::WRITABLE)
            .map_err(|err| {
                Error::new(
                    Kind::Internal,
                    format!(
                        "Encountered trigger write error when trying to reregister WebSocket connection {} token: {:?}",
                        err, token
                    ),
                )
            })
    }
}

pub struct WebSocket<F, const IN_LEN: usize, const OUT_LEN: usize>
where
    F: FnMut(&mut WebSocketHandle<IN_LEN, OUT_LEN>, CallbackData) -> Result<()>,
{
    handle: WebSocketHandle<IN_LEN, OUT_LEN>,
    settings: Settings,
    events: Events,
    callback: F,
}

impl<F, const IN_LEN: usize, const OUT_LEN: usize> WebSocket<F, IN_LEN, OUT_LEN>
where
    F: FnMut(&mut WebSocketHandle<IN_LEN, OUT_LEN>, CallbackData) -> Result<()>,
{
    pub fn new(settings: Settings, callback: F) -> Result<Self> {
        Ok(Self {
            handle: WebSocketHandle {
                connections: std::array::from_fn(|_| None),
                state: State::Inactive,
                poll: Poll::new()?,
            },
            settings,
            events: mio::Events::with_capacity(MAX_EVENTS),
            callback,
        })
    }

    pub fn handle_mut(&mut self) -> &mut WebSocketHandle<IN_LEN, OUT_LEN> {
        &mut self.handle
    }

    pub fn connect<T>(&mut self, url: T, token: Token) -> Result<()>
    where
        T: Into<Url>,
    {
        let url: Url = url.into();
        self.inner_connect(url, token, None)?;
        Ok(())
    }

    pub fn connect_with_headers<T>(
        &mut self,
        url: T,
        token: Token,
        headers: std::collections::HashMap<String, String>,
    ) -> Result<()>
    where
        T: Into<Url>,
    {
        let url: Url = url.into();
        self.inner_connect(url, token, Some(headers))?;
        Ok(())
    }

    fn inner_connect(
        &mut self,
        url: Url,
        tok: Token,
        headers: Option<std::collections::HashMap<String, String>>,
    ) -> Result<()> {
        match Connection::connect_with_headers(url, self.settings.clone(), headers) {
            Ok(mut conn) => {
                self.handle
                    .poll
                    .registry()
                    .register(
                        conn.socket_mut(),
                        tok,
                        Interest::READABLE | Interest::WRITABLE,
                    )
                    .map_err(|err| {
                        Error::new(
                            Kind::Internal,
                            format!(
                                "Encountered error when trying to build WebSocket connection {}",
                                err
                            ),
                        )
                    })?;
                self.handle.insert_connection(tok, conn)?;
                Ok(())
            }
            Err(e) => Err(e),
        }
    }

    pub fn handle_conn_close(&mut self, token: Token) -> Result<()> {
        let index = token.0;
        if index >= MAX_CONNECTIONS {
            return Err(Error::new(
                Kind::Internal,
                format!(
                    "Token index {} exceeds maximum connections {}",
                    index, MAX_CONNECTIONS
                ),
            ));
        }

        // Check if connection exists
        if self.handle.connections[index].is_none() {
            return Err(Error::new(
                Kind::Internal,
                format!("Connection not found for token: {:?}", token),
            ));
        }

        // First, deregister the socket from the poll
        {
            let conn = self.handle.connections[index].as_mut().unwrap();
            let socket = conn.socket_mut();
            match self.handle.poll.registry().deregister(socket) {
                Ok(_) => {}
                Err(err) => {
                    return Err(Error::new(
                        Kind::Internal,
                        format!(
                            "Encountered handle_conn_close error when trying to deregister WebSocket connection {}",
                            err
                        ),
                    ));
                }
            }
        }

        // Now handle shutdown and reconnection
        let should_reconnect = self.settings.auto_reconnect;

        let mut new_conn = {
            let mut conn = self.handle.connections[index].take().unwrap();
            let _ = conn.shutdown();
            if should_reconnect {
                match conn.reconnect() {
                    Ok(new_conn) => new_conn,
                    Err(err) => {
                        return Err(Error::new(
                            Kind::Internal,
                            format!(
                                "Encountered handle_conn_close error when trying to reconnect WebSocket connection {}",
                                err
                            ),
                        ));
                    }
                }
            } else {
                return Ok(());
            }
        };

        self.handle
            .poll
            .registry()
            .register(
                new_conn.socket_mut(),
                token,
                Interest::READABLE | Interest::WRITABLE,
            )
            .map_err(|err| {
                Error::new(
                    Kind::Internal,
                    format!(
                        "Encountered error when trying to build WebSocket connection {}",
                        err
                    ),
                )
            })?;
        self.handle.insert_connection(token, new_conn)
    }

    pub fn run(&mut self) -> Result<()> {
        self.handle.state = State::Active;
        self.event_loop()?;
        self.handle.state = State::Inactive;
        Ok(())
    }

    #[inline]
    fn event_loop(&mut self) -> Result<()> {
        while self.handle.state.is_active() {
            if self.handle.is_connections_empty() && self.settings.stop_when_no_connection {
                break;
            }
            self.handle
                .poll
                .poll(&mut self.events, self.settings.event_loop_timeout)?;
            self.handle_events()?;
        }
        Ok(())
    }

    fn handle_events(&mut self) -> Result<()> {
        for event in self.events.iter() {
            let token = event.token();
            if event.is_readable() {
                loop {
                    let conn = match self.handle.get_connection_mut(token) {
                        Some(conn) => conn,
                        None => {
                            return Err(Error::new(
                                Kind::Internal,
                                format!("Connection not found for token: {:?}", token),
                            ));
                        }
                    };
                    match conn.read() {
                        Ok(Some(Message::Open)) => {
                            (self.callback)(&mut self.handle, CallbackData::ConnectionOpen(token))?;
                        }
                        Ok(Some(msgs)) => {
                            (self.callback)(&mut self.handle, CallbackData::Message(token, msgs))?;
                        }
                        Ok(None) => {
                            break;
                        }
                        Err(err) => {
                            let _ = conn.shutdown(); // we make shutdown err silence here, we cannot do anything
                            match err.kind {
                                Kind::ClosedByPeer | Kind::Closing => {
                                    (self.callback)(
                                        &mut self.handle,
                                        CallbackData::ConnectionClose(token, err),
                                    )?;
                                }
                                _ => {
                                    (self.callback)(
                                        &mut self.handle,
                                        CallbackData::ConnectionError(token, err),
                                    )?;
                                }
                            }
                            break;
                        }
                    }
                }
            }
            if event.is_writable() {
                let write_all = if let Some(conn) = self.handle.get_connection_mut(token) {
                    match conn.write() {
                        Ok(true) => true,
                        Ok(false) => false,
                        Err(err) => return Err(err),
                    }
                } else {
                    return Err(Error::new(
                        Kind::Internal,
                        format!("Connection not found for token: {:?}", token),
                    ));
                };
                if write_all {
                    // Access the connection directly from the array to avoid borrowing conflicts
                    let index = token.0;
                    if index < MAX_CONNECTIONS {
                        if let Some(ref mut conn) = self.handle.connections[index] {
                            let socket = conn.socket_mut();
                            self.handle.poll.registry().reregister(
                                socket,
                                token,
                                Interest::READABLE,
                            ).map_err(|err| {
                                Error::new(
                                    Kind::Internal,
                                    format!(
                                        "Encountered handle_events error when trying to reregister WebSocket connection {} token: {:?}",
                                        err, token,
                                    ),
                                )
                            })?;
                        }
                    }
                }
            }
        }
        let to_close_tokens: Vec<_> = self
            .handle
            .connections
            .iter()
            .enumerate()
            .filter_map(|(index, conn_opt)| {
                if let Some(conn) = conn_opt {
                    if conn.is_closing() || conn.is_closed() {
                        Some(Token(index))
                    } else {
                        None
                    }
                } else {
                    None
                }
            })
            .collect();

        for t in to_close_tokens {
            self.handle_conn_close(t)?;
        }
        Ok(())
    }
}
