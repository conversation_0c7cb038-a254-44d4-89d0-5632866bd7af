use std::{
    collections::{HashMap, HashSet},
    fs::{self, File},
    io::Write,
};

#[derive(serde::Deserialize)]
struct Config {
    api_key: String,
    private_key_pem: String,
}

struct ParsedRing {
    edges: Vec<(String, String)>,
}

fn parse_config() -> Config {
    let input = fs::read_to_string("./config.json").expect(
        "Failed to read config.json. Please create config.json based on config.json.example",
    );
    serde_json::from_str(&input).expect("Invalid JSON format in config.json")
}

fn parse_price_tick() -> HashMap<String, Vec<f64>> {
    let input = fs::read_to_string("./scripts/bn_margin_order_filters.json")
        .expect("Failed to read bn_order_filters.json");
    serde_json::from_str(&input).expect("Invalid JSON format")
}

fn parse_rings() -> Vec<ParsedRing> {
    let input = fs::read_to_string("./scripts/bn_margin_circles.json")
        .expect("Failed to read bn_margin_circles.json");
    let rings: Vec<Vec<Vec<String>>> = serde_json::from_str(&input).expect("Invalid JSON format");
    rings
        .iter()
        .map(|ring| ParsedRing {
            edges: ring
                .iter()
                .map(|edge| {
                    assert_eq!(edge.len(), 2, "Invalid edge format");
                    (edge[0].clone(), edge[1].clone())
                })
                .collect(),
        })
        .collect()
}

fn def_edge_direction(f: &mut File) {
    writeln!(f, "#[derive(Debug, Clone, Copy)]").unwrap();
    writeln!(f, "#[repr(u8)]").unwrap();
    writeln!(f, "pub enum EdgeDirection {{").unwrap();
    writeln!(f, "    Forward,").unwrap();
    writeln!(f, "    Reverse,").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "impl EdgeDirection {{").unwrap();
    writeln!(f, "    pub fn to_str(&self) -> &'static str {{").unwrap();
    writeln!(f, "        match self {{").unwrap();
    writeln!(f, "            EdgeDirection::Forward => \"BUY\",").unwrap();
    writeln!(f, "            EdgeDirection::Reverse => \"SELL\",").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();
}

fn def_rings(f: &mut File, rings: Vec<ParsedRing>, pairs: &[String]) {
    writeln!(
        f,
        "pub static PREDEFINED_RINGS: &[&[(TradingPair, EdgeDirection)]] = &["
    )
    .unwrap();
    let mut index_to_pair = HashMap::new();
    let mut ring_lengths = Vec::new();
    for (ring_index, ring) in rings.iter().enumerate() {
        let edges: Vec<String> = ring
            .edges
            .iter()
            .map(|s| {
                let pair = s.0.as_str();
                if !index_to_pair.contains_key(pair) {
                    index_to_pair.insert(pair, Vec::new());
                }
                index_to_pair.get_mut(pair).unwrap().push(ring_index);
                let direction = s.1.as_str();
                let mut parts = pair.split('/');
                let base = parts.next().unwrap();
                let quote = parts.next().unwrap();
                let trading_pair = format!("TradingPair::X{}{}", base, quote);

                let edge_direction = match direction {
                    "forward" => "EdgeDirection::Forward",
                    "reverse" => "EdgeDirection::Reverse",
                    _ => panic!("Invalid direction: {}", direction),
                };
                format!("({}, {})", trading_pair, edge_direction)
            })
            .collect();
        ring_lengths.push(edges.len());
        writeln!(f, "    &[{}],", edges.join(", ")).unwrap();
    }
    writeln!(f, "];").unwrap();
    writeln!(f).unwrap();

    writeln!(
        f,
        "// order filled count in each ring, first is len, second is current filled count"
    )
    .unwrap();
    writeln!(
        f,
        "pub static mut RING_FILLED_ORDERS: [[u8; 3]; {}] = [",
        ring_lengths.len(),
    )
    .unwrap();
    for len in ring_lengths.iter() {
        writeln!(f, "    [{}, 0, 0],", len).unwrap();
    }
    writeln!(f, "];").unwrap();
    writeln!(f).unwrap();
    writeln!(
        f,
        "pub static mut RING_FILLED_PRICES: [[f64; 4]; {}] = [[0.0; 4]; {}];",
        ring_lengths.len(),
        ring_lengths.len(),
    )
    .unwrap();

    writeln!(f, "// order record in each ring").unwrap();
    writeln!(
        f,
        "// 0 is expect price, 1 is weighted avg fill price, 2 is order latency, 3 is total fill quantity, 4 is total fill amount"
    )
    .unwrap();

    writeln!(f, "pub static TRADING_PAIR_TO_RING_INDEX: &[&[u16]] = &[").unwrap();
    for pair in pairs {
        let ring_indices = index_to_pair.get(pair.as_str()).unwrap();
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "    &[{}], // TradingPair::{}{}",
            ring_indices
                .iter()
                .map(|x| x.to_string())
                .collect::<Vec<_>>()
                .join(", "),
            parts[0].to_uppercase(),
            parts[1].to_uppercase(),
        )
        .unwrap();
    }
    writeln!(f, "];").unwrap();
    writeln!(f).unwrap();
}

fn def_pair_rate(f: &mut File, len: usize) {
    writeln!(
        f,
        "pub static mut TRADING_PAIR_RATES: [[f64; 2]; {}] = [[0.0; 2]; {}];",
        len, len
    )
    .unwrap();
    writeln!(
        f,
        "pub static mut TRADING_PAIR_QTY: [[f64; 2]; {}] = [[0.0; 2]; {}];",
        len, len
    )
    .unwrap();
    writeln!(
        f,
        "pub static mut TRADING_PAIR_RATE_EVENT_TIME: [u64; {}] = [0; {}];",
        len, len
    )
    .unwrap();
    writeln!(
        f,
        "pub static mut TRADING_PAIR_RATE_UPDATE_ID: [u64; {}] = [0; {}];",
        len, len
    )
    .unwrap();
    writeln!(
        f,
        "// 0 for invalid, 1 for sbe bbo, 2 for sbe depth diff, 3 for sbe depth snapshot"
    )
    .unwrap();
    writeln!(
        f,
        "// 4 for ws bbo, 5 for ws depth diff, 6 for ws depth snapshot"
    )
    .unwrap();
    writeln!(
        f,
        "pub static mut TRADING_PAIR_RATE_FROM: [u8; {}] = [0; {}];",
        len, len
    )
    .unwrap();
    writeln!(
        f,
        "pub static mut TRADING_PAIR_RATE_UPDATE_TIME: [u64; {}] = [0; {}];",
        len, len
    )
    .unwrap();
}

fn extract_unique_pairs(rings: &[ParsedRing]) -> Vec<String> {
    let mut pair_set = HashSet::new();
    for ring in rings {
        for (pair, _) in &ring.edges {
            pair_set.insert(pair.clone());
        }
    }
    let mut pairs: Vec<String> = pair_set.into_iter().collect();
    pairs.sort();
    pairs
}

fn extract_unique_currencies(pairs: &[String]) -> Vec<String> {
    let mut currency_set = HashSet::new();
    for pair in pairs {
        let variants = pair.split('/').collect::<Vec<_>>();
        currency_set.insert(variants[0].to_uppercase());
        currency_set.insert(variants[1].to_uppercase());
    }
    currency_set.into_iter().collect()
}

fn def_trading_pair(f: &mut File, pairs: &[String], currencies: &[String]) {
    writeln!(f, "#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]").unwrap();
    writeln!(f, "#[repr(u16)]").unwrap();
    writeln!(f, "pub enum Currency {{").unwrap();
    for currency in currencies {
        writeln!(f, "    X{},", currency).unwrap();
    }
    writeln!(f, "    Unknown,").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "impl Currency {{").unwrap();
    writeln!(f, "    pub fn to_str(&self) -> &'static str {{").unwrap();
    writeln!(f, "        match self {{").unwrap();
    for currency in currencies {
        writeln!(
            f,
            "            Currency::X{} => \"{}\",",
            currency, currency
        )
        .unwrap();
    }
    writeln!(f, "            Currency::Unknown => \"UNKNOWN\",").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "    pub fn from_str(s: &str) -> Self {{").unwrap();
    writeln!(f, "        match s {{").unwrap();
    for currency in currencies {
        writeln!(
            f,
            "            \"{}\" => Currency::X{},",
            currency, currency
        )
        .unwrap();
    }
    writeln!(f, "            _ => Currency::Unknown,").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();

    writeln!(
        f,
        "pub static mut CURRENCY_BALANCES: [f64; {}] = [0.0; {}];",
        currencies.len(),
        currencies.len()
    )
    .unwrap();

    writeln!(f, "#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]").unwrap();
    writeln!(f, "#[repr(u8)]").unwrap();
    writeln!(f, "pub enum TradingPair {{").unwrap();
    for pair in pairs {
        let variants = pair.split('/').collect::<Vec<_>>();
        writeln!(
            f,
            "    X{}{},  // {}",
            variants[0].to_uppercase(),
            variants[1].to_uppercase(),
            pair,
        )
        .unwrap();
    }
    writeln!(f, "    Unknown,").unwrap();
    writeln!(f, "}}").unwrap();

    // 生成获取基础货币的方法
    writeln!(f, "impl TradingPair {{").unwrap();
    writeln!(f, "    pub fn base(&self) -> Currency {{").unwrap();
    writeln!(f, "        match self {{").unwrap();
    for pair in pairs {
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "            TradingPair::X{}{} => Currency::X{},",
            parts[0], parts[1], parts[0]
        )
        .unwrap();
    }
    writeln!(f, "            TradingPair::Unknown => Currency::Unknown,").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();

    writeln!(f, "    pub fn quote(&self) -> Currency {{").unwrap();
    writeln!(f, "        match self {{").unwrap();
    for pair in pairs {
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "            TradingPair::X{}{} => Currency::X{},",
            parts[0], parts[1], parts[1]
        )
        .unwrap();
    }
    writeln!(f, "            TradingPair::Unknown => Currency::Unknown,").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "impl TradingPair {{").unwrap();

    writeln!(f, "    pub fn all_symbols() -> Vec<&'static str> {{").unwrap();
    writeln!(f, "        vec![").unwrap();
    for pair in pairs {
        writeln!(
            f,
            "            \"{}\",",
            pair.replace("/", "").to_lowercase()
        )
        .unwrap();
    }
    writeln!(f, "        ]").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "    pub fn from_lower_case(s: &str) -> Self {{").unwrap();
    writeln!(f, "        match s {{").unwrap();
    for pair in pairs {
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "            \"{}\" => TradingPair::X{}{},",
            pair.replace("/", "").to_lowercase(),
            parts[0].to_uppercase(),
            parts[1].to_uppercase()
        )
        .unwrap();
    }
    writeln!(f, "            _ => TradingPair::Unknown,").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "impl From<&str> for TradingPair {{").unwrap();
    writeln!(f, "    fn from(s: &str) -> Self {{").unwrap();
    writeln!(f, "        match s {{").unwrap();
    for pair in pairs {
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "            \"{}\" => TradingPair::X{}{},",
            pair.replace("/", "").to_uppercase(),
            parts[0].to_uppercase(),
            parts[1].to_uppercase()
        )
        .unwrap();
    }
    writeln!(f, "            _ => TradingPair::Unknown,").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "impl From<usize> for TradingPair {{").unwrap();
    writeln!(f, "    fn from(num: usize) -> Self {{").unwrap();
    writeln!(f, "        match num {{").unwrap();
    for i in 0..pairs.len() {
        let pair = &pairs[i];
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "            {} => TradingPair::X{}{},",
            i,
            parts[0].to_uppercase(),
            parts[1].to_uppercase()
        )
        .unwrap();
    }
    writeln!(f, "            _ => TradingPair::Unknown,").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "impl From<(Currency, Currency)> for TradingPair {{").unwrap();
    writeln!(
        f,
        "    fn from((base, quote): (Currency, Currency)) -> Self {{"
    )
    .unwrap();
    writeln!(f, "        match (base, quote) {{").unwrap();
    for pair in pairs {
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "            (Currency::X{}, Currency::X{}) => TradingPair::X{}{},",
            parts[0].to_uppercase(),
            parts[1].to_uppercase(),
            parts[0].to_uppercase(),
            parts[1].to_uppercase()
        )
        .unwrap();
    }
    writeln!(f, "            _ => TradingPair::Unknown,").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();

    writeln!(f, "pub const TRADING_PAIR_COUNT: usize = {};", pairs.len()).unwrap();

    writeln!(f, "impl std::fmt::Display for TradingPair {{").unwrap();
    writeln!(
        f,
        "    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {{"
    )
    .unwrap();
    writeln!(f, "        let s = match self {{").unwrap();
    for pair in pairs {
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "            TradingPair::X{}{} => \"{}\",",
            parts[0].to_uppercase(),
            parts[1].to_uppercase(),
            pair.replace("/", "").to_uppercase(),
        )
        .unwrap();
    }
    writeln!(f, "            TradingPair::Unknown => \"UNKNOWN\",").unwrap();
    writeln!(f, "        }};").unwrap();
    writeln!(f, "        write!(f, \"{{}}\", s)").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "impl TradingPair {{").unwrap();
    writeln!(f, "    pub fn to_str(&self) -> &'static str {{").unwrap();
    writeln!(f, "        match self {{").unwrap();
    for pair in pairs {
        let parts: Vec<&str> = pair.split('/').collect();
        writeln!(
            f,
            "            TradingPair::X{}{} => \"{}\",",
            parts[0].to_uppercase(),
            parts[1].to_uppercase(),
            pair.replace("/", "").to_uppercase()
        )
        .unwrap();
    }
    writeln!(f, "            TradingPair::Unknown => \"UNKNOWN\",").unwrap();
    writeln!(f, "        }}").unwrap();
    writeln!(f, "    }}").unwrap();
    writeln!(f, "}}").unwrap();
    writeln!(f).unwrap();
}

fn def_order_filters(f: &mut File, price_ticks: &HashMap<String, Vec<f64>>, pairs: &[String]) {
    writeln!(f, "pub static ORDER_FILTER_PRICE_TICK_INDEX: usize = 0;").unwrap();
    writeln!(f, "pub static ORDER_FILTER_LOT_SIZE_INDEX: usize = 1;").unwrap();
    writeln!(f, "pub static ORDER_FILTER_MIN_ORDER_QTY_INDEX: usize = 2;").unwrap();
    writeln!(f, "pub static ORDER_FILTER_MIN_NOTIONAL_INDEX: usize = 3;").unwrap();
    writeln!(f, "pub static ORDER_FILTERS: &[[f64; 4]] = &[ // price_tick, lot_size, min_order_qty, min_notional").unwrap();
    for pair in pairs {
        let key = pair.replace("/", "");
        let tick = price_ticks.get(&key).unwrap();
        writeln!(
            f,
            "    [{}f64, {}f64, {}f64, {}f64],",
            tick[0], tick[1], tick[2], tick[3]
        )
        .unwrap();
    }
    writeln!(f, "];").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "pub static PRICE_TICK: [f64; {}] = [", pairs.len()).unwrap();
    for pair in pairs {
        let key = pair.replace("/", "");
        let tick = price_ticks.get(&key).unwrap();
        writeln!(f, "    {}f64, // {}", tick[0], key).unwrap();
    }
    writeln!(f, "];").unwrap();

    writeln!(f, "pub static LOT_SIZE_MULT: [u64; {}] = [", pairs.len()).unwrap();
    for pair in pairs {
        let key = pair.replace("/", "");
        let tick = price_ticks.get(&key).unwrap();
        writeln!(f, "    {},", (1.0 / tick[1]).round() as u64).unwrap();
    }
    writeln!(f, "];").unwrap();
}

fn def_trading_fees(f: &mut File, pairs: &[String]) {
    writeln!(f, "// Trading fees: [maker_commission, taker_commission]").unwrap();
    writeln!(
        f,
        "pub static mut TRADING_FEES: [[f64; 2]; {}] = [",
        pairs.len()
    )
    .unwrap();

    for pair in pairs {
        let symbol = pair.replace("/", "").to_uppercase();
        writeln!(f, "    [0.001f64, 0.001f64], // {} (default)", symbol).unwrap();
    }
    writeln!(f, "];").unwrap();
    writeln!(f).unwrap();

    writeln!(f, "pub static MAKER_FEE_INDEX: usize = 0;").unwrap();
    writeln!(f, "pub static TAKER_FEE_INDEX: usize = 1;").unwrap();
    writeln!(f).unwrap();
}

fn def_config_constants(f: &mut File, config: &Config) {
    writeln!(
        f,
        "// API configuration constants generated from config.json"
    )
    .unwrap();
    writeln!(f, "pub static API_KEY: &str = \"{}\";", config.api_key).unwrap();
    writeln!(
        f,
        "pub static PRIVATE_KEY_PEM: &str = r#\"{}\"#;",
        config.private_key_pem
    )
    .unwrap();
    writeln!(f).unwrap();
}

fn def_order_book_depth(f: &mut File, pair_len: usize) {
    writeln!(f, "use crate::engine::orderbook::OrderBook;").unwrap();
    writeln!(
        f,
        "pub static mut ORDER_BOOKS: [OrderBook<20>; {}]= [OrderBook::new(); {}];",
        pair_len, pair_len
    )
    .unwrap();
}

fn main() {
    let config = parse_config();
    let parsed_rings = parse_rings();
    let order_filters = parse_price_tick();

    let unique_pairs = extract_unique_pairs(&parsed_rings);
    let unique_currencies = extract_unique_currencies(&unique_pairs);

    let mut f = File::create("src/engine/trading_pair.rs").unwrap();
    def_config_constants(&mut f, &config);
    def_trading_pair(&mut f, &unique_pairs, &unique_currencies);
    def_edge_direction(&mut f);
    def_pair_rate(&mut f, unique_pairs.len());
    def_rings(&mut f, parsed_rings, &unique_pairs);
    def_order_filters(&mut f, &order_filters, &unique_pairs);
    def_trading_fees(&mut f, &unique_pairs);
    def_order_book_depth(&mut f, unique_pairs.len());
}
