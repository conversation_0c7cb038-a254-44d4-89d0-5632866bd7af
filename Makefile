# Makefile for libwebsocket-rs cross-compilation

# Default target
.DEFAULT_GOAL := help

# Variables
X64_TARGET := x86_64-unknown-linux-gnu
X64_MUSL_TARGET := x86_64-unknown-linux-musl
ARM64_TARGET := aarch64-unknown-linux-gnu
ARM64_MUSL_TARGET := aarch64-unknown-linux-musl
APPLE_ARM64_TARGET := aarch64-apple-darwin

# Colors
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
NC := \033[0m

.PHONY: help install-deps x64 x64-debug x64-musl x64-trace x64-info x64-warn x64-error arm64 arm64-debug arm64-musl arm64-trace arm64-info arm64-warn arm64-error apple-arm64 apple-arm64-trace apple-arm64-info apple-arm64-warn apple-arm64-error all all-debug clean clean-all test-x64 test-arm64 check-deps artifacts

help: ## Show this help message
	@echo "$(BLUE)libwebsocket-rs Build & Cross-compilation Makefile$(NC)"
	@echo ""
	@echo "$(YELLOW)x64 targets:$(NC)"
	@echo "  $(GREEN)x64$(NC)                  Build x64 release binary (GNU libc)"
	@echo "  $(GREEN)x64-debug$(NC)            Build x64 debug binary"
	@echo "  $(GREEN)x64-musl$(NC)             Build x64 release binary (musl libc)"
	@echo "  $(GREEN)x64-trace$(NC)            Build x64 release binary with TRACE logging"
	@echo "  $(GREEN)x64-info$(NC)             Build x64 release binary with INFO logging"
	@echo "  $(GREEN)x64-warn$(NC)             Build x64 release binary with WARN logging"
	@echo "  $(GREEN)x64-error$(NC)            Build x64 release binary with ERROR logging"
	@echo ""
	@echo "$(YELLOW)ARM64 targets:$(NC)"
	@echo "  $(GREEN)arm64$(NC)                Build ARM64 release binary (GNU libc)"
	@echo "  $(GREEN)arm64-debug$(NC)          Build ARM64 debug binary"
	@echo "  $(GREEN)arm64-musl$(NC)           Build ARM64 release binary (musl libc)"
	@echo "  $(GREEN)arm64-trace$(NC)          Build ARM64 release binary with TRACE logging"
	@echo "  $(GREEN)arm64-info$(NC)           Build ARM64 release binary with INFO logging"
	@echo "  $(GREEN)arm64-warn$(NC)           Build ARM64 release binary with WARN logging"
	@echo "  $(GREEN)arm64-error$(NC)          Build ARM64 release binary with ERROR logging"
	@echo "  $(GREEN)apple-arm64$(NC)          Build for Apple Silicon (macOS ARM64)"
	@echo "  $(GREEN)apple-arm64-trace$(NC)    Build Apple Silicon with TRACE logging"
	@echo "  $(GREEN)apple-arm64-info$(NC)     Build Apple Silicon with INFO logging"
	@echo "  $(GREEN)apple-arm64-warn$(NC)     Build Apple Silicon with WARN logging"
	@echo "  $(GREEN)apple-arm64-error$(NC)    Build Apple Silicon with ERROR logging"
	@echo ""
	@echo "$(YELLOW)Convenience targets:$(NC)"
	@echo "  $(GREEN)all$(NC)                  Build both x64 and ARM64 release binaries"
	@echo "  $(GREEN)all-debug$(NC)            Build both x64 and ARM64 debug binaries"
	@echo ""
	@echo "$(YELLOW)Utility targets:$(NC)"
	@echo "  $(GREEN)install-deps$(NC)         Install cross-compilation dependencies"
	@echo "  $(GREEN)check-deps$(NC)           Check if dependencies are installed"
	@echo "  $(GREEN)test-x64$(NC)             Run tests for x64 target"
	@echo "  $(GREEN)test-arm64$(NC)           Run tests for ARM64 target (requires qemu-user)"
	@echo "  $(GREEN)clean$(NC)                Clean all build artifacts"
	@echo "  $(GREEN)clean-all$(NC)            Clean all target artifacts"
	@echo "  $(GREEN)artifacts$(NC)            Show all built artifacts"

install-deps: ## Install cross-compilation dependencies
	@echo "$(BLUE)Installing cross-compilation dependencies...$(NC)"
	./build_arm64.sh --install-deps --target $(ARM64_TARGET)

# x64 targets
x64: ## Build x64 release binary (GNU libc)
	@echo "$(BLUE)Building x64 release binary (GNU libc)...$(NC)"
	cargo build --target $(X64_TARGET) --release

x64-debug: ## Build x64 debug binary
	@echo "$(BLUE)Building x64 debug binary...$(NC)"
	cargo build --target $(X64_TARGET)

x64-musl: ## Build x64 release binary (musl libc)
	@echo "$(BLUE)Building x64 release binary (musl libc)...$(NC)"
	cargo build --target $(X64_MUSL_TARGET) --release

x64-trace: ## Build x64 release binary with TRACE logging
	@echo "$(BLUE)Building x64 release binary with TRACE logging...$(NC)"
	cargo build --target $(X64_TARGET) --release --no-default-features --features log-trace

x64-info: ## Build x64 release binary with INFO logging
	@echo "$(BLUE)Building x64 release binary with INFO logging...$(NC)"
	cargo build --target $(X64_TARGET) --release --no-default-features --features log-info

x64-warn: ## Build x64 release binary with WARN logging
	@echo "$(BLUE)Building x64 release binary with WARN logging...$(NC)"
	cargo build --target $(X64_TARGET) --release --no-default-features --features log-warn

x64-error: ## Build x64 release binary with ERROR logging
	@echo "$(BLUE)Building x64 release binary with ERROR logging...$(NC)"
	cargo build --target $(X64_TARGET) --release --no-default-features --features log-error

# ARM64 targets
arm64: ## Build ARM64 release binary (GNU libc)
	@echo "$(BLUE)Building ARM64 release binary (GNU libc)...$(NC)"
	./build_arm64.sh --target $(ARM64_TARGET)

arm64-debug: ## Build ARM64 debug binary
	@echo "$(BLUE)Building ARM64 debug binary...$(NC)"
	./build_arm64.sh --target $(ARM64_TARGET) --debug

arm64-musl: ## Build ARM64 release binary (musl libc)
	@echo "$(BLUE)Building ARM64 release binary (musl libc)...$(NC)"
	./build_arm64.sh --target $(ARM64_MUSL_TARGET)

arm64-trace: ## Build ARM64 release binary with TRACE logging
	@echo "$(BLUE)Building ARM64 release binary with TRACE logging...$(NC)"
	CARGO_FEATURES="--no-default-features --features log-trace" ./build_arm64.sh --target $(ARM64_TARGET)

arm64-info: ## Build ARM64 release binary with INFO logging
	@echo "$(BLUE)Building ARM64 release binary with INFO logging...$(NC)"
	CARGO_FEATURES="--no-default-features --features log-info" ./build_arm64.sh --target $(ARM64_TARGET)

arm64-warn: ## Build ARM64 release binary with WARN logging
	@echo "$(BLUE)Building ARM64 release binary with WARN logging...$(NC)"
	CARGO_FEATURES="--no-default-features --features log-warn" ./build_arm64.sh --target $(ARM64_TARGET)

arm64-error: ## Build ARM64 release binary with ERROR logging
	@echo "$(BLUE)Building ARM64 release binary with ERROR logging...$(NC)"
	CARGO_FEATURES="--no-default-features --features log-error" ./build_arm64.sh --target $(ARM64_TARGET)

apple-arm64: ## Build for Apple Silicon (macOS ARM64)
	@echo "$(BLUE)Building for Apple Silicon...$(NC)"
	cargo build --target $(APPLE_ARM64_TARGET) --release

apple-arm64-trace: ## Build Apple Silicon with TRACE logging
	@echo "$(BLUE)Building Apple Silicon with TRACE logging...$(NC)"
	cargo build --target $(APPLE_ARM64_TARGET) --release --no-default-features --features log-trace

apple-arm64-info: ## Build Apple Silicon with INFO logging
	@echo "$(BLUE)Building Apple Silicon with INFO logging...$(NC)"
	cargo build --target $(APPLE_ARM64_TARGET) --release --no-default-features --features log-info

apple-arm64-warn: ## Build Apple Silicon with WARN logging
	@echo "$(BLUE)Building Apple Silicon with WARN logging...$(NC)"
	cargo build --target $(APPLE_ARM64_TARGET) --release --no-default-features --features log-warn

apple-arm64-error: ## Build Apple Silicon with ERROR logging
	@echo "$(BLUE)Building Apple Silicon with ERROR logging...$(NC)"
	cargo build --target $(APPLE_ARM64_TARGET) --release --no-default-features --features log-error

# Convenience targets
all: x64 arm64 ## Build both x64 and ARM64 release binaries

all-debug: x64-debug arm64-debug ## Build both x64 and ARM64 debug binaries

# Test targets
test-x64: ## Run tests for x64 target
	@echo "$(BLUE)Running tests for x64...$(NC)"
	cargo test --target $(X64_TARGET)

test-arm64: ## Run tests for ARM64 target (requires qemu-user)
	@echo "$(YELLOW)Note: This requires qemu-user-static to be installed$(NC)"
	cargo test --target $(ARM64_TARGET)

# Clean targets
clean: ## Clean all build artifacts
	cargo clean

clean-all: ## Clean all target artifacts
	rm -rf target/$(X64_TARGET)
	rm -rf target/$(X64_MUSL_TARGET)
	rm -rf target/$(ARM64_TARGET)
	rm -rf target/$(ARM64_MUSL_TARGET)
	rm -rf target/$(APPLE_ARM64_TARGET)

# Check dependencies
check-deps: ## Check if cross-compilation dependencies are installed
	@echo "$(BLUE)Checking build dependencies...$(NC)"
	@echo "$(YELLOW)x64 targets:$(NC)"
	@rustup target list --installed | grep -q $(X64_TARGET) && echo "$(GREEN)✓ $(X64_TARGET) target installed$(NC)" || echo "$(YELLOW)✗ $(X64_TARGET) target not installed$(NC)"
	@rustup target list --installed | grep -q $(X64_MUSL_TARGET) && echo "$(GREEN)✓ $(X64_MUSL_TARGET) target installed$(NC)" || echo "$(YELLOW)✗ $(X64_MUSL_TARGET) target not installed$(NC)"
	@echo "$(YELLOW)ARM64 cross-compilation:$(NC)"
	@command -v aarch64-linux-gnu-gcc >/dev/null 2>&1 && echo "$(GREEN)✓ aarch64-linux-gnu-gcc found$(NC)" || echo "$(YELLOW)✗ aarch64-linux-gnu-gcc not found$(NC)"
	@rustup target list --installed | grep -q $(ARM64_TARGET) && echo "$(GREEN)✓ $(ARM64_TARGET) target installed$(NC)" || echo "$(YELLOW)✗ $(ARM64_TARGET) target not installed$(NC)"
	@rustup target list --installed | grep -q $(ARM64_MUSL_TARGET) && echo "$(GREEN)✓ $(ARM64_MUSL_TARGET) target installed$(NC)" || echo "$(YELLOW)✗ $(ARM64_MUSL_TARGET) target not installed$(NC)"

# Show build artifacts
artifacts: ## Show all built artifacts
	@echo "$(BLUE)x64 build artifacts:$(NC)"
	@find target -name "*x86_64*" -type d 2>/dev/null | head -10 || echo "No x64 artifacts found"
	@echo ""
	@echo "$(BLUE)ARM64 build artifacts:$(NC)"
	@find target -name "*arm*" -o -name "*aarch64*" -type d 2>/dev/null | head -10 || echo "No ARM64 artifacts found"
	@echo ""
	@echo "$(BLUE)Built binaries:$(NC)"
	@find target/*/release -name "arb" -o -name "libwebsocket-rs" 2>/dev/null | head -10 || echo "No binaries found"
