{"cells": [{"cell_type": "code", "execution_count": null, "id": "b813a990", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["pnl 0.5707984625304722 max 0.5917984625304721 min -0.0623600754030968\n"]}], "source": ["import pandas as pd\n", "\n", "pnl = 0\n", "max_pnl = 0\n", "min_pnl = 0\n", "data = pd.read_csv(\"20250722_091716_analysis.csv\")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "817bd403", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["win rate avg 1.0012993595410724\n", "loss rate avg 0.9996005268263727\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_419870/1850345204.py:1: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  win_rate_avg = data[data['win_loss'] == \"Win\"][data['real_rate'] < 1.01]['real_rate'].mean()\n"]}], "source": ["win_rate_avg = data[data['win_loss'] == \"Win\"][data['real_rate'] < 1.01]['real_rate'].mean()\n", "loss_rate_avg = data[data['win_loss'] == \"Loss\"]['real_rate'].mean()\n", "\n", "print(f\"win rate avg {win_rate_avg}\")\n", "print(f\"loss rate avg {loss_rate_avg}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "131b0443", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(154.66666666666666)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["data[data['win_loss'] == \"Win\"]['real_rate'][data['real_rate'] < 1.01].count() / 3"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}