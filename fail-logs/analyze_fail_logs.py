#!/usr/bin/env python3
"""
分析fail-logs日志文件，提取每个trigger symbol对应的日志时间、交易对信息、Win/Loss结果、real_rate和expect_rate等字段
"""

import re
import json
from datetime import datetime
from typing import List, Dict, Any
import argparse


def parse_log_file(file_path: str) -> List[Dict[str, Any]]:
    """
    解析日志文件，提取trigger symbol相关信息

    Args:
        file_path: 日志文件路径

    Returns:
        包含解析结果的列表，每个元素包含trigger symbol及其相关交易对信息
    """
    results = []

    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # 查找trigger symbol行
        trigger_match = re.search(r'Trigger symbol: (\w+)', line)
        if trigger_match:
            trigger_symbol = trigger_match.group(1)

            # 提取当前行的时间戳
            time_match = re.search(r'\[([^\]]+)\]', line)
            log_time = time_match.group(1) if time_match else ""

            # 向前查找 Expect rate 信息（通常在 Trigger symbol 前面几行）
            expect_rate = None
            for j in range(max(0, i-10), i):
                prev_line = lines[j].strip()
                expect_rate_match = re.search(r'Expect rate: ([\d.]+)', prev_line)
                if expect_rate_match:
                    expect_rate = float(expect_rate_match.group(1))
                    break

            # 向后查找相关的交易对信息和Win/Loss信息
            trading_pairs = []
            win_loss = None
            real_rate = None
            k = i + 1

            # 向后搜索最多100行来找到交易对信息和Win/Loss信息
            while k < len(lines) and k < i + 100:
                next_line = lines[k].strip()

                # 查找包含交易对信息的行 (新格式: SYMBOL p: price t: timestamp l: latency)
                pair_match = re.search(
                    r'(\w+) p: ([\d.]+) t: (\d+) l: ([\d.]+)',
                    next_line
                )

                if pair_match:
                    symbol = pair_match.group(1)
                    price = float(pair_match.group(2))
                    timestamp = int(pair_match.group(3))
                    latency = float(pair_match.group(4))

                    # 提取该行的时间戳
                    time_match = re.search(r'\[([^\]]+)\]', next_line)
                    pair_log_time = time_match.group(1) if time_match else ""

                    trading_pairs.append({
                        'symbol': symbol,
                        'price': price,
                        'timestamp': timestamp,
                        'latency': latency,
                        'log_time': pair_log_time
                    })

                # 查找Win!或Loss!
                if "Win!" in next_line:
                    win_loss = "Win"
                elif "Loss!" in next_line:
                    win_loss = "Loss"

                # 查找Real rate信息
                rate_match = re.search(r'Real rate: ([\d.]+)', next_line)
                if rate_match and real_rate is None:
                    real_rate = float(rate_match.group(1))

                # 如果找到了Win/Loss信息，说明这个套利操作结束了
                if win_loss is not None:
                    break

                k += 1

            # 添加到结果中
            result_entry = {
                'trigger_symbol': trigger_symbol,
                'trigger_log_time': log_time,
                'trading_pairs': trading_pairs,
                'total_pairs': len(trading_pairs),
                'win_loss': win_loss,
                'real_rate': real_rate,
                'expect_rate': expect_rate
            }

            results.append(result_entry)

        i += 1

    return results


def format_timestamp(timestamp: int) -> str:
    """
    将时间戳转换为可读格式

    Args:
        timestamp: 时间戳（微秒）

    Returns:
        格式化的时间字符串
    """
    try:
        # 假设时间戳是微秒级别的
        dt = datetime.fromtimestamp(timestamp / 1000000)
        return dt.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]  # 保留毫秒
    except:
        return str(timestamp)


def parse_log_time_to_microseconds(log_time_str: str) -> int:
    """
    将日志时间字符串转换为微秒时间戳

    Args:
        log_time_str: 日志时间字符串，格式如 "2025-07-15 13:54:09.132054"

    Returns:
        微秒时间戳
    """
    try:
        dt = datetime.strptime(log_time_str, '%Y-%m-%d %H:%M:%S.%f')
        return int(dt.timestamp() * 1000000)
    except:
        return 0


def calculate_time_differences(trading_pairs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    计算时间差值

    Args:
        trading_pairs: 交易对列表

    Returns:
        添加了时间差值的交易对列表
    """
    enhanced_pairs = []

    for pair in trading_pairs:
        enhanced_pair = pair.copy()

        # 计算 log_time - timestamp 的差值（毫秒）
        log_time_us = parse_log_time_to_microseconds(pair['log_time'])
        timestamp = pair['timestamp']

        # log_time - timestamp (转换为毫秒)
        log_timestamp_diff_ms = (log_time_us - timestamp) / 1000 if log_time_us > 0 else None

        enhanced_pair['log_timestamp_diff_ms'] = log_timestamp_diff_ms

        enhanced_pairs.append(enhanced_pair)

    return enhanced_pairs


def print_analysis_summary(results: List[Dict[str, Any]]):
    """
    打印分析摘要

    Args:
        results: 解析结果列表
    """
    print(f"\n=== 日志分析摘要 ===")
    print(f"总共找到 {len(results)} 个trigger symbol事件")

    # 统计Win/Loss
    win_count = 0
    loss_count = 0
    unknown_count = 0

    for result in results:
        win_loss = result.get('win_loss')
        if win_loss == 'Win':
            win_count += 1
        elif win_loss == 'Loss':
            loss_count += 1
        else:
            unknown_count += 1

    print(f"\nWin/Loss 统计:")
    print(f"  Win: {win_count} 次")
    print(f"  Loss: {loss_count} 次")
    if unknown_count > 0:
        print(f"  未知: {unknown_count} 次")

    if win_count + loss_count > 0:
        win_rate = win_count / (win_count + loss_count) * 100
        print(f"  胜率: {win_rate:.2f}%")

    # 统计每个trigger symbol的出现次数
    trigger_counts = {}
    trigger_win_loss = {}

    for result in results:
        symbol = result['trigger_symbol']
        win_loss = result.get('win_loss') or 'Unknown'

        trigger_counts[symbol] = trigger_counts.get(symbol, 0) + 1

        if symbol not in trigger_win_loss:
            trigger_win_loss[symbol] = {'Win': 0, 'Loss': 0, 'Unknown': 0}
        trigger_win_loss[symbol][win_loss] += 1

    print(f"\nTrigger Symbol 统计:")
    for symbol, count in sorted(trigger_counts.items(), key=lambda x: x[1], reverse=True):
        win_loss_stats = trigger_win_loss[symbol]
        win_rate_symbol = 0
        if win_loss_stats['Win'] + win_loss_stats['Loss'] > 0:
            win_rate_symbol = win_loss_stats['Win'] / (win_loss_stats['Win'] + win_loss_stats['Loss']) * 100

        print(f"  {symbol}: {count} 次 (Win: {win_loss_stats['Win']}, Loss: {win_loss_stats['Loss']}, 胜率: {win_rate_symbol:.1f}%)")

    # 统计交易对信息
    all_symbols = set()
    for result in results:
        for pair in result['trading_pairs']:
            all_symbols.add(pair['symbol'])

    print(f"\n涉及的交易对总数: {len(all_symbols)}")
    print(f"交易对列表: {', '.join(sorted(all_symbols))}")


def save_to_csv(results: List[Dict[str, Any]], output_file: str):
    """
    将结果保存为CSV文件

    Args:
        results: 解析结果列表
        output_file: 输出文件路径
    """
    import csv

    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'trigger_symbol', 'trigger_log_time', 'win_loss', 'real_rate', 'expect_rate',
            'pair_symbol', 'pair_log_time', 'price', 'timestamp', 'latency',
            'timestamp_formatted', 'log_timestamp_diff_ms'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for result in results:
            trigger_symbol = result['trigger_symbol']
            trigger_log_time = result['trigger_log_time']
            win_loss = result.get('win_loss', '')
            real_rate = result.get('real_rate', '')
            expect_rate = result.get('expect_rate', '')

            # 计算时间差值
            enhanced_pairs = calculate_time_differences(result['trading_pairs'])

            for pair in enhanced_pairs:
                writer.writerow({
                    'trigger_symbol': trigger_symbol,
                    'trigger_log_time': trigger_log_time,
                    'win_loss': win_loss,
                    'real_rate': real_rate,
                    'expect_rate': expect_rate,
                    'pair_symbol': pair['symbol'],
                    'pair_log_time': pair['log_time'],
                    'price': pair['price'],
                    'timestamp': pair['timestamp'],
                    'latency': pair['latency'],
                    'timestamp_formatted': format_timestamp(pair['timestamp']),
                    'log_timestamp_diff_ms': pair.get('log_timestamp_diff_ms', '')
                })


def main():
    parser = argparse.ArgumentParser(description='分析fail-logs日志文件')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('--output', '-o', help='输出CSV文件路径', default='analysis_result.csv')
    parser.add_argument('--json', help='输出JSON文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')

    args = parser.parse_args()

    print(f"正在分析日志文件: {args.log_file}")

    # 解析日志文件
    results = parse_log_file(args.log_file)

    # 打印摘要
    print_analysis_summary(results)

    # 保存为CSV
    save_to_csv(results, args.output)
    print(f"\n结果已保存到CSV文件: {args.output}")

    # 如果指定了JSON输出
    if args.json:
        with open(args.json, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"结果已保存到JSON文件: {args.json}")

    # 如果启用详细模式，打印每个trigger symbol的详细信息
    if args.verbose:
        print(f"\n=== 详细信息 ===")
        for i, result in enumerate(results, 1):
            print(f"\n{i}. Trigger Symbol: {result['trigger_symbol']}")
            print(f"   触发时间: {result['trigger_log_time']}")
            print(f"   结果: {result.get('win_loss', 'Unknown')}")
            print(f"   Real Rate: {result.get('real_rate', 'N/A')}")
            print(f"   Expect Rate: {result.get('expect_rate', 'N/A')}")
            print(f"   相关交易对数量: {result['total_pairs']}")

            # 计算时间差值
            enhanced_pairs = calculate_time_differences(result['trading_pairs'])

            for j, pair in enumerate(enhanced_pairs, 1):
                print(f"   {j}. {pair['symbol']}")
                print(f"      日志时间: {pair['log_time']}")
                print(f"      价格: {pair['price']}")
                print(f"      时间戳: {pair['timestamp']} ({format_timestamp(pair['timestamp'])})")
                print(f"      延迟: {pair['latency']} ms")
                print(f"      时间差 - log_time - timestamp: {pair.get('log_timestamp_diff_ms', 'N/A')} ms")


if __name__ == "__main__":
    main()
