#!/usr/bin/env python3
"""
提取日志文件中Win!和Loss!时的Real Rate数据
"""

import re
import csv
from datetime import datetime
from typing import List, Dict, Any
import argparse


def extract_win_loss_rates(file_path: str) -> List[Dict[str, Any]]:
    """
    提取日志文件中Win!和Loss!时的Real Rate数据

    Args:
        file_path: 日志文件路径

    Returns:
        包含Win和Loss时Real Rate信息的列表
    """
    rates_data = []

    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # 查找trigger symbol行
        trigger_match = re.search(r'Trigger symbol: (\w+)', line)
        if trigger_match:
            trigger_symbol = trigger_match.group(1)

            # 提取当前行的时间戳
            time_match = re.search(r'\[([^\]]+)\]', line)
            trigger_time = time_match.group(1) if time_match else ""

            # 向后查找Win!/Loss!和Real rate信息
            real_rate = None
            result_time = None
            result_type = None
            k = i + 1
            
            # 向后搜索最多100行来找到Win!/Loss!和Real rate信息
            while k < len(lines) and k < i + 100:
                next_line = lines[k].strip()

                # 查找Real rate信息
                rate_match = re.search(r'Real rate: ([\d.]+)', next_line)
                if rate_match and real_rate is None:
                    real_rate = float(rate_match.group(1))

                # 查找Win!
                if "Win!" in next_line:
                    # 提取Win!行的时间戳
                    time_match = re.search(r'\[([^\]]+)\]', next_line)
                    result_time = time_match.group(1) if time_match else ""
                    result_type = "Win"
                    
                    # 如果找到了Win!，记录数据
                    if real_rate is not None:
                        rates_data.append({
                            'trigger_symbol': trigger_symbol,
                            'trigger_time': trigger_time,
                            'result_time': result_time,
                            'result_type': result_type,
                            'real_rate': real_rate
                        })
                    break
                
                # 查找Loss!
                elif "Loss!" in next_line:
                    # 提取Loss!行的时间戳
                    time_match = re.search(r'\[([^\]]+)\]', next_line)
                    result_time = time_match.group(1) if time_match else ""
                    result_type = "Loss"
                    
                    # 如果找到了Loss!，记录数据
                    if real_rate is not None:
                        rates_data.append({
                            'trigger_symbol': trigger_symbol,
                            'trigger_time': trigger_time,
                            'result_time': result_time,
                            'result_type': result_type,
                            'real_rate': real_rate
                        })
                    break

                k += 1

        i += 1

    return rates_data


def save_rates_to_csv(rates_data: List[Dict[str, Any]], output_file: str):
    """
    将Win和Loss时的Real Rate数据保存为CSV文件

    Args:
        rates_data: Win和Loss时的Real Rate数据列表
        output_file: 输出文件路径
    """
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'trigger_symbol', 'trigger_time', 'result_time', 'result_type', 'real_rate'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for rate_data in rates_data:
            writer.writerow(rate_data)


def print_summary(rates_data: List[Dict[str, Any]]):
    """
    打印Win和Loss时Real Rate的统计摘要

    Args:
        rates_data: Win和Loss时的Real Rate数据列表
    """
    if not rates_data:
        print("没有找到Win!或Loss!的记录")
        return

    # 分离Win和Loss数据
    win_data = [data for data in rates_data if data['result_type'] == 'Win']
    loss_data = [data for data in rates_data if data['result_type'] == 'Loss']

    print(f"\n=== Win/Loss Real Rate统计摘要 ===")
    print(f"总共找到 {len(rates_data)} 条记录")
    print(f"  Win: {len(win_data)} 次")
    print(f"  Loss: {len(loss_data)} 次")
    
    if len(win_data) + len(loss_data) > 0:
        win_rate = len(win_data) / (len(win_data) + len(loss_data)) * 100
        print(f"  胜率: {win_rate:.2f}%")

    # Win时的Real Rate统计
    if win_data:
        win_rates = [data['real_rate'] for data in win_data]
        print(f"\nWin时Real Rate统计:")
        print(f"  最小值: {min(win_rates):.6f}")
        print(f"  最大值: {max(win_rates):.6f}")
        print(f"  平均值: {sum(win_rates)/len(win_rates):.6f}")

    # Loss时的Real Rate统计
    if loss_data:
        loss_rates = [data['real_rate'] for data in loss_data]
        print(f"\nLoss时Real Rate统计:")
        print(f"  最小值: {min(loss_rates):.6f}")
        print(f"  最大值: {max(loss_rates):.6f}")
        print(f"  平均值: {sum(loss_rates)/len(loss_rates):.6f}")

    # 统计每个trigger symbol的Win/Loss次数和平均Real Rate
    symbol_stats = {}
    for data in rates_data:
        symbol = data['trigger_symbol']
        result_type = data['result_type']
        rate = data['real_rate']
        
        if symbol not in symbol_stats:
            symbol_stats[symbol] = {'Win': [], 'Loss': []}
        
        symbol_stats[symbol][result_type].append(rate)

    print(f"\n各Trigger Symbol的统计:")
    for symbol, stats in sorted(symbol_stats.items(), 
                               key=lambda x: len(x[1]['Win']) + len(x[1]['Loss']), 
                               reverse=True):
        win_count = len(stats['Win'])
        loss_count = len(stats['Loss'])
        total_count = win_count + loss_count
        
        if total_count > 0:
            symbol_win_rate = win_count / total_count * 100
            
            win_avg = sum(stats['Win']) / len(stats['Win']) if stats['Win'] else 0
            loss_avg = sum(stats['Loss']) / len(stats['Loss']) if stats['Loss'] else 0
            
            print(f"  {symbol}: {total_count}次 (Win:{win_count}, Loss:{loss_count}, 胜率:{symbol_win_rate:.1f}%)")
            if stats['Win']:
                print(f"    Win平均Rate: {win_avg:.6f}")
            if stats['Loss']:
                print(f"    Loss平均Rate: {loss_avg:.6f}")


def main():
    parser = argparse.ArgumentParser(description='提取日志文件中Win!和Loss!时的Real Rate数据')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('--output', '-o', help='输出CSV文件路径', default='win_loss_rates.csv')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')

    args = parser.parse_args()

    print(f"正在分析日志文件: {args.log_file}")

    # 提取Win和Loss时的Real Rate数据
    rates_data = extract_win_loss_rates(args.log_file)

    # 打印统计摘要
    print_summary(rates_data)

    # 保存为CSV
    save_rates_to_csv(rates_data, args.output)
    print(f"\nWin和Loss时的Real Rate数据已保存到: {args.output}")
    print(f"总共保存了 {len(rates_data)} 条记录")

    # 如果启用详细模式，打印每条记录
    if args.verbose:
        print(f"\n=== 详细记录 ===")
        for i, data in enumerate(rates_data, 1):
            print(f"{i}. {data['trigger_symbol']} - {data['result_type']} - Rate: {data['real_rate']:.6f}")
            print(f"   触发时间: {data['trigger_time']}")
            print(f"   结果时间: {data['result_time']}")


if __name__ == "__main__":
    main()
