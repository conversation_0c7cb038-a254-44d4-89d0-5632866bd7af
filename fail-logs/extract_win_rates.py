#!/usr/bin/env python3
"""
提取日志文件中Win!时的Real Rate数据
"""

import re
import csv
from datetime import datetime
from typing import List, Dict, Any
import argparse


def extract_win_rates(file_path: str) -> List[Dict[str, Any]]:
    """
    提取日志文件中Win!时的Real Rate数据

    Args:
        file_path: 日志文件路径

    Returns:
        包含Win时Real Rate信息的列表
    """
    win_rates = []

    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    i = 0
    while i < len(lines):
        line = lines[i].strip()

        # 查找trigger symbol行
        trigger_match = re.search(r'Trigger symbol: (\w+)', line)
        if trigger_match:
            trigger_symbol = trigger_match.group(1)

            # 提取当前行的时间戳
            time_match = re.search(r'\[([^\]]+)\]', line)
            trigger_time = time_match.group(1) if time_match else ""

            # 向后查找Win!和Real rate信息
            real_rate = None
            win_time = None
            k = i + 1

            # 向后搜索最多100行来找到Win!和Real rate信息
            while k < len(lines) and k < i + 100:
                next_line = lines[k].strip()

                # 查找Real rate信息
                rate_match = re.search(r'Real rate: ([\d.]+)', next_line)
                if rate_match and real_rate is None:
                    real_rate = float(rate_match.group(1))

                # 查找Win!
                if "Win!" in next_line or "Loss!" in next_line:
                    # 提取Win!行的时间戳
                    time_match = re.search(r'\[([^\]]+)\]', next_line)
                    win_time = time_match.group(1) if time_match else ""

                    # 如果找到了Win!，记录数据
                    if real_rate is not None:
                        win_rates.append({
                            'trigger_symbol': trigger_symbol,
                            'trigger_time': trigger_time,
                            'win_time': win_time,
                            'win_or_loss': 1 if "Win!" in next_line else 0,
                            'real_rate': real_rate
                        })
                    break


                k += 1

        i += 1

    return win_rates


def save_win_rates_to_csv(win_rates: List[Dict[str, Any]], output_file: str):
    """
    将Win时的Real Rate数据保存为CSV文件

    Args:
        win_rates: Win时的Real Rate数据列表
        output_file: 输出文件路径
    """
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = [
            'trigger_symbol', 'trigger_time', 'win_time', 'real_rate'
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for rate_data in win_rates:
            writer.writerow(rate_data)


def print_win_summary(win_rates: List[Dict[str, Any]]):
    """
    打印Win时Real Rate的统计摘要

    Args:
        win_rates: Win时的Real Rate数据列表
    """
    if not win_rates:
        print("没有找到Win!的记录")
        return

    print(f"\n=== Win时Real Rate统计摘要 ===")
    print(f"总共找到 {len(win_rates)} 次Win记录")

    # 计算Real Rate统计
    rates = [data['real_rate'] for data in win_rates]
    min_rate = min(rates)
    max_rate = max(rates)
    avg_rate = sum(rates) / len(rates)

    print(f"\nReal Rate统计:")
    print(f"  最小值: {min_rate:.6f}")
    print(f"  最大值: {max_rate:.6f}")
    print(f"  平均值: {avg_rate:.6f}")

    # 统计每个trigger symbol的Win次数和平均Real Rate
    symbol_stats = {}
    for data in win_rates:
        symbol = data['trigger_symbol']
        rate = data['real_rate']

        if symbol not in symbol_stats:
            symbol_stats[symbol] = {'count': 0, 'rates': []}

        symbol_stats[symbol]['count'] += 1
        symbol_stats[symbol]['rates'].append(rate)

    print(f"\n各Trigger Symbol的Win统计:")
    for symbol, stats in sorted(symbol_stats.items(), key=lambda x: x[1]['count'], reverse=True):
        count = stats['count']
        avg_symbol_rate = sum(stats['rates']) / len(stats['rates'])
        min_symbol_rate = min(stats['rates'])
        max_symbol_rate = max(stats['rates'])

        print(f"  {symbol}: {count}次Win, 平均Rate: {avg_symbol_rate:.6f}, 范围: {min_symbol_rate:.6f}-{max_symbol_rate:.6f}")


def main():
    parser = argparse.ArgumentParser(description='提取日志文件中Win!时的Real Rate数据')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('--output', '-o', help='输出CSV文件路径', default='win_rates.csv')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')

    args = parser.parse_args()

    print(f"正在分析日志文件: {args.log_file}")

    # 提取Win时的Real Rate数据
    win_rates = extract_win_rates(args.log_file)

    # 打印统计摘要
    print_win_summary(win_rates)

    # 保存为CSV
    save_win_rates_to_csv(win_rates, args.output)
    print(f"\nWin时的Real Rate数据已保存到: {args.output}")
    print(f"总共保存了 {len(win_rates)} 条记录")

    # 如果启用详细模式，打印每条Win记录
    if args.verbose:
        print(f"\n=== 详细Win记录 ===")
        for i, data in enumerate(win_rates, 1):
            print(f"{i}. {data['trigger_symbol']} - Rate: {data['real_rate']:.6f}")
            print(f"   触发时间: {data['trigger_time']}")
            print(f"   Win时间: {data['win_time']}")


if __name__ == "__main__":
    main()
