#!/usr/bin/env python3
"""
triangle_screen_threshold.py — 评分并筛选出现 ≥0.1 % 三角套利机会的币
加入：API 超时 / 连接重置自动重试（默认 5 次指数退避）

核心公式
    score = faces × log10(volume+1) × σ × depth_factor × hit_rate
"""

from __future__ import annotations
import argparse, time, math, sys, traceback
from collections import defaultdict

import requests

BINANCE = "https://api.binance.com"
QUOTE_WHITELIST = {"USDT", "BTC", "ETH", "BNB", "FDUSD", "USDC"}
DEPTH_PRICE_RANGE = 0.0005  # ±0.05 %
MIN_DEPTH_USD = 10_000  # 低于此深度按比例打折


# ──────────────────────── 通用请求封装 ────────────────────────
def fetch(path: str, retries=5, backoff=5, **params):
    """带重试的 GET。retries 次失败后抛异常"""
    for attempt in range(1, retries + 1):
        try:
            r = requests.get(f"{BINANCE}{path}", params=params, timeout=15)  # 15 s 超时
            r.raise_for_status()
            return r.json()
        except Exception as e:
            if attempt == retries:
                raise  # 最后一次仍失败，让上层决定
            wait = backoff * attempt
            print(
                f"    ⚠️ API 调用失败：{e} | 第 {attempt}/{retries} 次重试… " f"等待 {wait} 秒",
                file=sys.stderr,
                flush=True,
            )
            time.sleep(wait)


# 简单包装原有接口
exchange_info = lambda **kw: fetch("/api/v3/exchangeInfo", **kw)["symbols"]
stats24 = lambda **kw: fetch("/api/v3/ticker/24hr", **kw)
depth_snapshot = lambda symbol, **kw: fetch("/api/v3/depth", symbol=symbol, limit=1000, **kw)


def klines(symbol: str, start_ms: int, end_ms: int, sleep_sec: float, retries: int, backoff: int):
    """拉取 [start_ms, end_ms) 的 1 min K 线，内部也带重试"""
    out = []
    while start_ms < end_ms:
        batch = fetch(
            "/api/v3/klines",
            symbol=symbol,
            interval="1m",
            startTime=start_ms,
            endTime=min(end_ms, start_ms + 1000 * 60_000),
            limit=1000,
            retries=retries,
            backoff=backoff,
        )
        if not batch:
            break
        out.extend(float(c[4]) for c in batch)
        start_ms = batch[-1][0] + 60_000
        time.sleep(sleep_sec)
    return out


# ────────────────────────── 工具函数 ──────────────────────────
def usd_depth(snapshot, mid, usd_per_quote=1.0):
    rng = DEPTH_PRICE_RANGE
    bid_sum = 0.0
    for price, qty in snapshot["bids"]:
        p = float(price)
        if p < mid * (1 - rng):
            break
        bid_sum += float(qty) * p * usd_per_quote
    ask_sum = 0.0
    for price, qty in snapshot["asks"]:
        p = float(price)
        if p > mid * (1 + rng):
            break
        ask_sum += float(qty) * p * usd_per_quote
    return min(bid_sum, ask_sum)


def stdev(vals):
    n = len(vals)
    if n < 2:
        return 0.0
    m = sum(vals) / n
    return (sum((x - m) ** 2 for x in vals) / (n - 1)) ** 0.5


# ────────────────────────── 主流程 ────────────────────────────
def main():
    p = argparse.ArgumentParser()
    p.add_argument("--threshold", type=float, default=0.001, help="最小可执行价差 (0.001 = 0.1%%)")
    p.add_argument("--days", type=int, default=7)
    p.add_argument("--limit", type=int, default=50, help="先用成交额粗筛的币数")
    p.add_argument("--top", type=int, default=20)
    p.add_argument("--sleep", type=float, default=0.08, help="每 1000 根 kline 拉完后的 sleep 秒数")
    p.add_argument("--retries", type=int, default=5, help="API 调用最大重试次数")
    p.add_argument("--backoff", type=int, default=5, help="第一次重试等待秒数 (随后指数增加)")
    args = p.parse_args()

    print(">>> 拉取交易所元数据 …")
    symbols = exchange_info(retries=args.retries, backoff=args.backoff)
    print(f">>> 获取到 {len(symbols)} 个交易对，开始过滤 price_tick 占比 …")

    base_to_quotes, sym_name = defaultdict(set), {}
    for s in symbols:
        if s["status"] != "TRADING":
            continue

        # 检查 price_tick 占比条件
        price_tick = None
        for filter_info in s.get("filters", []):
            if filter_info.get("filterType") == "PRICE_FILTER":
                price_tick = float(filter_info.get("tickSize", "0"))
                break

        if price_tick and price_tick > 0:
            # 获取当前价格来计算占比
            try:
                current_price_data = fetch("/api/v3/ticker/price", symbol=s["symbol"], retries=1, backoff=1)
                current_price = float(current_price_data["price"])
                if current_price > 0:
                    tick_ratio = price_tick / current_price
                    if tick_ratio >= 0.001:  # 如果占比 >= 0.1%，跳过这个交易对
                        print(f"    跳过 {s['symbol']}: price_tick占比 {tick_ratio:.4f} >= 0.001")
                        continue
            except Exception as e:
                # 如果获取价格失败，跳过这个交易对
                print(f"    跳过 {s['symbol']}: 获取价格失败 {e}")
                continue

        b, q = s["baseAsset"], s["quoteAsset"]
        base_to_quotes[b].add(q)
        sym_name[(b, q)] = s["symbol"]

    print(f">>> 过滤后剩余 {len(sym_name)} 个交易对")
    print(">>> 拉取 24 h 成交额 …")
    vol_map = {s["symbol"]: float(s["quoteVolume"]) for s in stats24(retries=args.retries, backoff=args.backoff)}

    # ① 粗筛
    cand = []
    for base, qs in base_to_quotes.items():
        usable = qs & QUOTE_WHITELIST
        if len(usable) < 2:
            continue
        vol_sum = sum(vol_map.get(sym_name[(base, q)], 0) for q in usable)
        basic = len(usable) * math.log10(vol_sum + 1)
        cand.append((base, usable, vol_sum, basic))
    cand.sort(key=lambda x: x[3], reverse=True)
    cand = cand[: args.limit]
    print(f">>> 第一轮筛后深分析 {len(cand)} 个币")

    end_ms = int(time.time() * 1000)
    start_ms = end_ms - args.days * 24 * 60 * 60 * 1000
    btc_price = float(
        fetch("/api/v3/ticker/price", symbol="BTCUSDT", retries=args.retries, backoff=args.backoff)["price"]
    )

    results = []
    for idx, (base, qs, vol_sum, basic) in enumerate(cand, 1):
        print(f"[{idx:02}/{len(cand)}] 处理 {base} …", flush=True)

        # 选三条腿（优先 USDT + BTC）
        qlist = sorted(qs)
        q1, q2 = ("USDT", "BTC") if {"USDT", "BTC"} <= set(qlist) else qlist[:2]
        leg1 = sym_name[(base, q1)]
        leg2 = sym_name[(base, q2)]
        leg3 = sym_name.get((q2, q1)) or sym_name.get((q1, q2)) or "BTCUSDT"

        # --- 2.1 K 线 ---------------------------------------------------------
        try:
            p_b_q1 = klines(leg1, start_ms, end_ms, args.sleep, args.retries, args.backoff)
            p_b_q2 = klines(leg2, start_ms, end_ms, args.sleep, args.retries, args.backoff)
            p_cross = klines(leg3, start_ms, end_ms, args.sleep, args.retries, args.backoff)
        except Exception as e:
            print(f"    ❌ kline 多次失败：{e}")
            continue

        m = min(len(p_b_q1), len(p_b_q2), len(p_cross))
        if m < 200:
            print("    ⚠️ 数据点不足 (<200)，跳过")
            continue

        # 判断 leg3 的方向：q1/q2 还是 q2/q1
        if leg3.startswith(q1):  # q1q2, 价格= q1 in q2
            price_q1_q2 = p_cross
        else:  # q2q1, 先取倒数
            price_q1_q2 = [1 / x for x in p_cross]

        # 计算 spread（取绝对值，这样正/反方向都算）
        spreads = [abs(p_b_q1[i] / (p_b_q2[i] * price_q1_q2[i]) - 1) for i in range(m)]
        sigma = stdev(spreads)
        hits = sum(1 for x in spreads if x > args.threshold)
        hit_rate = hits / m
        if hit_rate == 0:
            print("    命中率 0%，跳过")
            continue

        # 2.2 深度
        try:
            d1 = depth_snapshot(leg1, retries=args.retries, backoff=args.backoff)
            d2 = depth_snapshot(leg2, retries=args.retries, backoff=args.backoff)
            d3 = depth_snapshot(leg3, retries=args.retries, backoff=args.backoff)
        except Exception as e:
            print(f"    ❌ depth 多次失败：{e}")
            traceback.print_exc(limit=1)
            continue
        mid1 = (float(d1["bids"][0][0]) + float(d1["asks"][0][0])) / 2
        mid2 = (float(d2["bids"][0][0]) + float(d2["asks"][0][0])) / 2
        mid3 = (float(d3["bids"][0][0]) + float(d3["asks"][0][0])) / 2
        dep1 = usd_depth(d1, mid1)
        dep2 = usd_depth(d2, mid2, usd_per_quote=(btc_price if q2 == "BTC" else 1))
        dep3 = usd_depth(d3, mid3)
        min_dep = min(dep1, dep2, dep3)
        depth_factor = min(1.0, min_dep / MIN_DEPTH_USD)

        score = basic * sigma * depth_factor * hit_rate
        results.append(
            {
                "base": base,
                "faces": len(qs),
                "vol": vol_sum,
                "sigma": sigma,
                "hit": hit_rate,
                "depth": min_dep,
                "score": score,
                "triangle": (leg1, leg2, leg3),
            }
        )

    # ③ 输出
    results.sort(key=lambda r: r["score"], reverse=True)
    top = results[: args.top]
    print("\n=========== 最终结果 ===========")
    print(f"阈值: {args.threshold:.3%} | 窗口: {args.days} d | 输出: Top {args.top}\n")
    header = "{:<6} {:>5} {:>12} {:>8} {:>7} {:>9} {:>9}".format(
        "Coin", "Faces", "24hVolM", "σ", "Hit%", "Depth$", "Score"
    )
    print(header)
    print("-" * len(header))
    for r in top:
        print(
            "{:<6} {:>5} {:>12.1f} {:>8.4f} {:>7.2%} {:>9.0f} {:>9.2f}".format(
                r["base"], r["faces"], r["vol"] / 1_000_000, r["sigma"], r["hit"], r["depth"], r["score"]
            )
        )
        print("    ", " ↔ ".join(r["triangle"]))

    print("\n全部完成。")


if __name__ == "__main__":
    main()
