#!/usr/bin/env python3
"""
现货期货对冲脚本
查找现货大于10 USDT的持仓，然后在期货市场下对应的对冲仓位
优先使用USDT，没有的话使用USDC，都没有则报错
"""

import json
import time
import logging
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Tuple, Optional

try:
    from binance.client import Client
    from binance.exceptions import BinanceAPIException
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False
    # 创建占位符类以避免语法错误
    class Client:
        pass
    class BinanceAPIException(Exception):
        pass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SpotFuturesHedger:
    def __init__(self, config_path: str = '../config.json'):
        """初始化对冲器"""
        if not BINANCE_AVAILABLE:
            raise ImportError("python-binance库未安装。请运行: pip install python-binance")

        self.config = self._load_config(config_path)

        # 检查必要的配置
        if 'api_key' not in self.config:
            raise ValueError("配置文件中缺少 api_key")
        if 'api_secret' not in self.config:
            raise ValueError("配置文件中缺少 api_secret")

        # 创建现货和期货客户端
        self.spot_client = Client(
            api_key=self.config['api_key'],
            api_secret=self.config['api_secret'],
            testnet=False
        )

        # 期货客户端使用相同的API密钥
        self.futures_client = Client(
            api_key=self.config['api_key'],
            api_secret=self.config['api_secret'],
            testnet=False
        )

        # 最小持仓价值阈值（USDT）
        self.min_position_value = Decimal('10.0')

    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"配置文件 {config_path} 不存在")
            raise
        except json.JSONDecodeError:
            logger.error(f"配置文件 {config_path} 格式错误")
            raise

    def get_spot_balances(self) -> Dict[str, Decimal]:
        """获取现货账户余额（只返回非零余额）"""
        try:
            account_info = self.spot_client.get_account()
            balances = {}

            for balance in account_info['balances']:
                asset = balance['asset']
                free = Decimal(balance['free'])
                locked = Decimal(balance['locked'])
                total = free + locked

                if total > 0:
                    balances[asset] = total

            logger.info(f"获取到 {len(balances)} 个非零现货余额")
            return balances
        except BinanceAPIException as e:
            logger.error(f"获取现货账户余额失败: {e}")
            raise

    def get_futures_account_info(self) -> dict:
        """获取期货账户信息"""
        try:
            return self.futures_client.futures_account()
        except BinanceAPIException as e:
            logger.error(f"获取期货账户信息失败: {e}")
            raise

    def get_spot_prices(self, symbols: List[str]) -> Dict[str, Decimal]:
        """获取现货价格信息"""
        try:
            prices = {}
            ticker_prices = self.spot_client.get_all_tickers()

            for ticker in ticker_prices:
                symbol = ticker['symbol']
                if symbol in symbols:
                    prices[symbol] = Decimal(ticker['price'])

            return prices
        except BinanceAPIException as e:
            logger.error(f"获取现货价格信息失败: {e}")
            raise

    def get_futures_exchange_info(self) -> dict:
        """获取期货交易对信息"""
        try:
            return self.futures_client.futures_exchange_info()
        except BinanceAPIException as e:
            logger.error(f"获取期货交易对信息失败: {e}")
            raise

    def calculate_spot_usdt_values(self, balances: Dict[str, Decimal]) -> Dict[str, Tuple[Decimal, Decimal]]:
        """计算每个现货币种的USDT价值，返回 {asset: (balance, usdt_value)}"""
        usdt_values = {}

        # USDT本身
        if 'USDT' in balances:
            usdt_values['USDT'] = (balances['USDT'], balances['USDT'])

        # 其他币种需要获取对USDT的价格
        symbols_needed = []
        for asset in balances:
            if asset != 'USDT':
                symbols_needed.append(f"{asset}USDT")

        if symbols_needed:
            prices = self.get_spot_prices(symbols_needed)

            for asset, balance in balances.items():
                if asset == 'USDT':
                    continue

                symbol = f"{asset}USDT"
                if symbol in prices:
                    usdt_value = balance * prices[symbol]
                    usdt_values[asset] = (balance, usdt_value)
                    logger.info(f"{asset}: {balance} * {prices[symbol]} = {usdt_value} USDT")
                else:
                    logger.warning(f"无法获取 {symbol} 的价格")

        return usdt_values

    def find_hedgeable_positions(self, usdt_values: Dict[str, Tuple[Decimal, Decimal]]) -> List[Tuple[str, Decimal, Decimal]]:
        """找出大于10 USDT的持仓，返回 [(asset, balance, usdt_value)]"""
        hedgeable = []

        for asset, (balance, usdt_value) in usdt_values.items():
            if usdt_value >= self.min_position_value:
                hedgeable.append((asset, balance, usdt_value))
                logger.info(f"可对冲持仓: {asset} = {usdt_value} USDT")

        return hedgeable

    def get_available_futures_symbols(self) -> Dict[str, dict]:
        """获取可用的期货交易对信息"""
        exchange_info = self.get_futures_exchange_info()
        futures_symbols = {}

        for symbol_info in exchange_info['symbols']:
            if symbol_info['status'] == 'TRADING' and symbol_info['contractType'] == 'PERPETUAL':
                base_asset = symbol_info['baseAsset']
                quote_asset = symbol_info['quoteAsset']
                futures_symbols[f"{base_asset}{quote_asset}"] = symbol_info

        logger.info(f"获取到 {len(futures_symbols)} 个可用期货交易对")
        return futures_symbols

    def find_futures_symbol_for_asset(self, asset: str, futures_symbols: Dict[str, dict]) -> Optional[str]:
        """为给定资产找到合适的期货交易对，优先USDT，然后USDC"""
        # 优先查找USDT交易对
        usdt_symbol = f"{asset}USDT"
        if usdt_symbol in futures_symbols:
            return usdt_symbol

        # 如果没有USDT，查找USDC交易对
        usdc_symbol = f"{asset}USDC"
        if usdc_symbol in futures_symbols:
            return usdc_symbol

        return None

    def check_futures_balance(self, quote_asset: str) -> Decimal:
        """检查期货账户中指定币种的余额"""
        account_info = self.get_futures_account_info()

        for asset_info in account_info['assets']:
            if asset_info['asset'] == quote_asset:
                return Decimal(asset_info['availableBalance'])

        return Decimal('0')

    def get_futures_symbol_info(self, symbol: str, futures_symbols: Dict[str, dict]) -> Optional[dict]:
        """获取期货交易对的详细信息"""
        return futures_symbols.get(symbol)

    def calculate_hedge_quantity(self, symbol: str, balance: Decimal, futures_symbols: Dict[str, dict]) -> Decimal:
        """计算对冲数量，考虑最小下单量等限制"""
        symbol_info = self.get_futures_symbol_info(symbol, futures_symbols)
        if not symbol_info:
            return balance

        # 获取数量精度
        quantity_precision = 0
        for filter_info in symbol_info.get('filters', []):
            if filter_info['filterType'] == 'LOT_SIZE':
                step_size = Decimal(filter_info['stepSize'])
                # 计算精度位数
                if step_size < 1:
                    quantity_precision = len(str(step_size).split('.')[-1].rstrip('0'))
                break

        # 按精度调整数量
        if quantity_precision > 0:
            return balance.quantize(Decimal('0.' + '0' * (quantity_precision - 1) + '1'), rounding=ROUND_DOWN)
        else:
            return balance.quantize(Decimal('1'), rounding=ROUND_DOWN)

    def place_futures_hedge_order(self, symbol: str, quantity: Decimal, futures_symbols: Dict[str, dict], dry_run: bool = True) -> bool:
        """下期货对冲订单（做空）"""
        try:
            # 计算精确的对冲数量
            hedge_quantity = self.calculate_hedge_quantity(symbol, quantity, futures_symbols)

            if hedge_quantity <= 0:
                logger.error(f"计算的对冲数量无效: {hedge_quantity}")
                return False

            if dry_run:
                logger.info(f"[模拟] 期货做空订单: {symbol}, 数量: {hedge_quantity}")
                return True

            # 实际下单
            order = self.futures_client.futures_create_order(
                symbol=symbol,
                side='SELL',  # 做空对冲
                type='MARKET',
                quantity=str(hedge_quantity)
            )

            logger.info(f"期货做空订单成功: {order}")
            return True

        except BinanceAPIException as e:
            logger.error(f"期货下单失败 {symbol}: {e}")
            return False

    def execute_hedge_strategy(self, dry_run: bool = True):
        """执行对冲策略"""
        logger.info("开始执行现货期货对冲策略...")

        # 1. 获取现货余额
        logger.info("获取现货账户余额...")
        spot_balances = self.get_spot_balances()

        if not spot_balances:
            logger.warning("没有发现任何现货余额")
            return

        # 2. 计算USDT价值
        logger.info("计算现货持仓USDT价值...")
        usdt_values = self.calculate_spot_usdt_values(spot_balances)

        # 3. 找出可对冲的持仓
        logger.info("筛选可对冲持仓...")
        hedgeable_positions = self.find_hedgeable_positions(usdt_values)

        if not hedgeable_positions:
            logger.info("没有发现大于10 USDT的持仓")
            return

        # 4. 获取期货交易对信息
        logger.info("获取期货交易对信息...")
        futures_symbols = self.get_available_futures_symbols()

        # 5. 为每个持仓执行对冲
        successful_hedges = 0
        failed_hedges = []

        for asset, balance, usdt_value in hedgeable_positions:
            logger.info(f"\n处理 {asset} 持仓对冲...")

            # 跳过稳定币
            if asset in ['USDT', 'USDC', 'BUSD', 'FDUSD', 'TUSD']:
                logger.info(f"跳过稳定币 {asset}")
                continue

            # 查找合适的期货交易对
            futures_symbol = self.find_futures_symbol_for_asset(asset, futures_symbols)

            if not futures_symbol:
                logger.error(f"❌ {asset} 没有找到合适的期货交易对")
                failed_hedges.append((asset, "没有期货交易对"))
                continue

            # 检查期货账户余额
            quote_asset = futures_symbol.replace(asset, '')  # 提取报价币种
            futures_balance = self.check_futures_balance(quote_asset)

            logger.info(f"期货账户 {quote_asset} 余额: {futures_balance}")

            # 检查余额是否足够
            if futures_balance < usdt_value:
                error_msg = f"期货账户 {quote_asset} 余额不足: {futures_balance} < {usdt_value}"
                logger.error(f"❌ {error_msg}")
                failed_hedges.append((asset, error_msg))
                continue

            # 下对冲订单
            logger.info(f"在期货市场做空 {futures_symbol}，对冲现货 {asset} 持仓")

            if self.place_futures_hedge_order(futures_symbol, balance, futures_symbols, dry_run):
                successful_hedges += 1
                logger.info(f"✅ {asset} 对冲成功")
            else:
                failed_hedges.append((asset, "下单失败"))

        # 6. 总结结果
        logger.info(f"\n=== 对冲结果总结 ===")
        logger.info(f"成功对冲: {successful_hedges} 个持仓")

        if failed_hedges:
            logger.info(f"失败对冲: {len(failed_hedges)} 个持仓")
            for asset, reason in failed_hedges:
                logger.error(f"  {asset}: {reason}")

        if not successful_hedges and failed_hedges:
            raise Exception("所有对冲操作都失败了！请检查期货账户余额和交易对可用性。")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='现货期货对冲脚本')
    parser.add_argument('--config', default='../config.json', help='配置文件路径')
    parser.add_argument('--dry-run', action='store_true', help='模拟运行，不实际下单')

    args = parser.parse_args()

    try:
        hedger = SpotFuturesHedger(args.config)
        hedger.execute_hedge_strategy(dry_run=args.dry_run)

    except Exception as e:
        logger.error(f"执行失败: {e}")
        return 1

    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())
