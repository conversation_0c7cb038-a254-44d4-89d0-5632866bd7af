#!/usr/bin/env python3
"""
测试现货期货对冲脚本的配置和连接
"""

import json
import sys
import logging
from decimal import Decimal

try:
    from binance.client import Client
    from binance.exceptions import BinanceAPIException
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_config(config_path: str = '../config.json') -> dict:
    """加载配置文件"""
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"配置文件 {config_path} 不存在")
        return None
    except json.JSONDecodeError:
        logger.error(f"配置文件 {config_path} 格式错误")
        return None

def test_spot_connection(config: dict) -> bool:
    """测试现货API连接"""
    try:
        client = Client(
            api_key=config['api_key'],
            api_secret=config['api_secret'],
            testnet=False
        )
        
        # 测试连接
        account_info = client.get_account()
        logger.info(f"✅ 现货账户连接成功")
        logger.info(f"账户类型: {account_info.get('accountType', 'Unknown')}")
        
        # 显示余额
        balances = []
        for balance in account_info['balances']:
            asset = balance['asset']
            free = float(balance['free'])
            locked = float(balance['locked'])
            total = free + locked
            
            if total > 0:
                balances.append((asset, total))
        
        logger.info(f"现货非零余额数量: {len(balances)}")
        for asset, total in balances[:5]:  # 只显示前5个
            logger.info(f"  {asset}: {total}")
        
        return True
        
    except BinanceAPIException as e:
        logger.error(f"❌ 现货API错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 现货连接错误: {e}")
        return False

def test_futures_connection(config: dict) -> bool:
    """测试期货API连接"""
    try:
        client = Client(
            api_key=config['api_key'],
            api_secret=config['api_secret'],
            testnet=False
        )
        
        # 测试期货账户
        account_info = client.futures_account()
        logger.info(f"✅ 期货账户连接成功")
        
        # 显示期货余额
        total_balance = Decimal(account_info['totalWalletBalance'])
        available_balance = Decimal(account_info['availableBalance'])
        
        logger.info(f"期货账户总余额: {total_balance} USDT")
        logger.info(f"期货账户可用余额: {available_balance} USDT")
        
        # 检查是否有足够余额进行对冲
        if available_balance < 100:
            logger.warning(f"⚠️  期货账户余额较低，可能无法进行大额对冲")
        
        return True
        
    except BinanceAPIException as e:
        logger.error(f"❌ 期货API错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 期货连接错误: {e}")
        return False

def test_futures_symbols(config: dict) -> bool:
    """测试期货交易对获取"""
    try:
        client = Client(
            api_key=config['api_key'],
            api_secret=config['api_secret'],
            testnet=False
        )
        
        exchange_info = client.futures_exchange_info()
        
        # 统计可用的期货交易对
        usdt_symbols = []
        usdc_symbols = []
        
        for symbol_info in exchange_info['symbols']:
            if symbol_info['status'] == 'TRADING' and symbol_info['contractType'] == 'PERPETUAL':
                symbol = symbol_info['symbol']
                if symbol.endswith('USDT'):
                    usdt_symbols.append(symbol)
                elif symbol.endswith('USDC'):
                    usdc_symbols.append(symbol)
        
        logger.info(f"✅ 期货交易对获取成功")
        logger.info(f"USDT永续合约数量: {len(usdt_symbols)}")
        logger.info(f"USDC永续合约数量: {len(usdc_symbols)}")
        
        # 显示一些常见的交易对
        common_assets = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL']
        logger.info("常见币种期货交易对检查:")
        
        for asset in common_assets:
            usdt_symbol = f"{asset}USDT"
            usdc_symbol = f"{asset}USDC"
            
            has_usdt = usdt_symbol in usdt_symbols
            has_usdc = usdc_symbol in usdc_symbols
            
            status = "✅" if (has_usdt or has_usdc) else "❌"
            available = []
            if has_usdt:
                available.append("USDT")
            if has_usdc:
                available.append("USDC")
            
            logger.info(f"  {status} {asset}: {', '.join(available) if available else '无'}")
        
        return True
        
    except BinanceAPIException as e:
        logger.error(f"❌ 期货交易对获取失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 期货交易对获取错误: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试现货期货对冲脚本配置')
    parser.add_argument('--config', default='../config.json', help='配置文件路径')
    
    args = parser.parse_args()
    
    logger.info("=== 现货期货对冲脚本配置测试 ===")
    
    # 检查binance库
    if not BINANCE_AVAILABLE:
        logger.error("❌ python-binance库未安装")
        logger.info("请运行: pip install python-binance")
        return 1
    
    logger.info("✅ python-binance库已安装")
    
    # 加载配置
    logger.info("检查配置文件...")
    config = load_config(args.config)
    if not config:
        return 1
    
    # 检查必要的配置
    if 'api_key' not in config:
        logger.error("❌ 配置文件中缺少 api_key")
        return 1
    if 'api_secret' not in config:
        logger.error("❌ 配置文件中缺少 api_secret")
        return 1
    
    logger.info("✅ 配置文件格式正确")
    
    # 测试现货连接
    logger.info("\n测试现货API连接...")
    if not test_spot_connection(config):
        return 1
    
    # 测试期货连接
    logger.info("\n测试期货API连接...")
    if not test_futures_connection(config):
        return 1
    
    # 测试期货交易对
    logger.info("\n测试期货交易对...")
    if not test_futures_symbols(config):
        return 1
    
    logger.info("\n=== 所有测试通过 ===")
    logger.info("✅ 现货期货对冲脚本配置正确，可以正常使用")
    logger.info("\n下一步:")
    logger.info("1. 模拟运行: python3 spot_futures_hedge.py --dry-run")
    logger.info("2. 实际运行: python3 spot_futures_hedge.py")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
