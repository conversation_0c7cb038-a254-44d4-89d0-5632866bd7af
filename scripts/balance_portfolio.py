#!/usr/bin/env python3
"""
Binance现货持仓平衡脚本
自动将所有持仓调整为每个币种持有20 USDT，多余的钱换成USDT
同时对所有现货币种（除了稳定币）进行永续合约对冲

SOL XRP BNB ETH SUI BCH APT ETC NEAR INJ ADA LTC USDT USDC DOT XLM HBAR POL UNI SEI BTC
"""

import json
import time
import logging
from decimal import Decimal, ROUND_DOWN, ROUND_HALF_UP
from typing import Dict, List, Tuple, Optional
from binance.um_futures import UMFutures

from binance.client import Client
from binance.error import ClientError
from binance.exceptions import BinanceAPIException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

TARGET_POSITION_USDT = '25.0'
TARGET_LEVERAGE = 40

class PortfolioBalancer:
    def __init__(self, config_path: str = 'portfolio_config.json'):
        self.config = self._load_config(config_path)

        # 检查必要的配置
        if 'api_key' not in self.config:
            raise ValueError("配置文件中缺少 api_key")
        if 'api_secret' not in self.config:
            raise ValueError("配置文件中缺少 api_secret，现货交易需要API Secret")

        # 创建客户端（统一账户模式下现货和期货使用相同的客户端）
        self.spot_client = Client(
            api_key=self.config['api_key'],
            api_secret=self.config['api_secret'],
            testnet=False  # 设置为True使用测试网
        )
        self.futures_client =  client = UMFutures(key=self.config['api_key'], secret=self.config['api_secret'])
        # 目标持仓金额（USDT）
        self.target_position_usdt = Decimal(TARGET_POSITION_USDT)
        # 最小交易金额（USDT）
        self.min_trade_amount = Decimal('1.0')
        # 价格容差（避免频繁小额交易）
        self.tolerance = Decimal('0.05')  # 5%
        # 稳定币列表（不需要对冲）
        self.stable_coins = {'USDT', 'USDC', 'BUSD', 'FDUSD', 'TUSD', 'DAI'}
        # 目标币种列表（将在运行时设置）
        self.target_assets = set()

    def set_target_assets(self, assets: List[str]):
        """设置目标持仓币种列表"""
        self.target_assets = set(asset.upper() for asset in assets)
        # 确保USDT始终在目标列表中
        self.target_assets.add('USDT')
        logger.info(f"设置目标币种: {', '.join(sorted(self.target_assets))}")

    def identify_assets_to_liquidate(self, balances: Dict[str, Decimal]) -> List[str]:
        """识别需要平仓的币种（不在目标列表中的币种）"""
        if not self.target_assets:
            logger.warning("目标币种列表为空，不会平仓任何币种")
            return []

        assets_to_liquidate = []
        for asset in balances:
            if asset not in self.target_assets and balances[asset] > 0:
                assets_to_liquidate.append(asset)

        if assets_to_liquidate:
            logger.info(f"发现需要平仓的币种: {', '.join(assets_to_liquidate)}")
        else:
            logger.info("没有发现需要平仓的币种")

        return assets_to_liquidate

    def liquidate_spot_positions(self, assets_to_liquidate: List[str], dry_run: bool = True) -> bool:
        """平仓指定的现货持仓"""
        if not assets_to_liquidate:
            return True

        logger.info("开始平仓现货持仓...")
        successful_liquidations = 0
        failed_liquidations = []

        for asset in assets_to_liquidate:
            try:
                # 获取当前余额
                account_info = self.spot_client.get_account()
                asset_balance = None

                for balance in account_info['balances']:
                    if balance['asset'] == asset:
                        free = Decimal(balance['free'])
                        locked = Decimal(balance['locked'])
                        total = free + locked
                        if total > 0:
                            asset_balance = total
                        break

                if not asset_balance or asset_balance <= 0:
                    logger.info(f"{asset} 余额为0，跳过")
                    continue

                # 构造交易对符号
                symbol = f"{asset}USDT"

                # 获取交易对信息
                try:
                    symbol_info = self.spot_client.get_symbol_info(symbol)
                except:
                    logger.error(f"无法获取 {symbol} 交易对信息，跳过平仓")
                    failed_liquidations.append((asset, "无法获取交易对信息"))
                    continue

                # 应用数量精度和检查最小名义价值
                quantity = asset_balance
                min_notional = None
                for filter_item in symbol_info['filters']:
                    if filter_item['filterType'] == 'LOT_SIZE':
                        step_size = Decimal(filter_item['stepSize'])
                        # 按照 step_size 进行量化：先除以 step_size，向下取整，再乘以 step_size
                        quantity = (quantity / step_size).quantize(Decimal('1'), rounding=ROUND_DOWN) * step_size
                    elif filter_item['filterType'] == 'MIN_NOTIONAL':
                        min_notional = Decimal(filter_item['minNotional'])

                if quantity <= 0:
                    logger.warning(f"{asset} 调整后数量为0，跳过")
                    continue

                # 获取当前价格并检查最小名义价值
                try:
                    ticker = self.spot_client.get_symbol_ticker(symbol=symbol)
                    current_price = Decimal(ticker['price'])
                    notional_value = quantity * current_price

                    if min_notional and notional_value < min_notional:
                        logger.warning(f"平仓 {asset} 的交易金额 {notional_value} USDT 小于最小名义价值 {min_notional} USDT，跳过")
                        continue

                    logger.info(f"平仓现货 {asset}: 数量 {quantity}, 当前价格 {current_price}, 名义价值 {notional_value} USDT")
                except Exception as e:
                    logger.error(f"获取 {symbol} 价格失败: {e}")
                    continue

                if dry_run:
                    logger.info(f"[模拟] 卖出 {symbol}: 数量 {quantity}")
                    successful_liquidations += 1
                else:
                    # 执行市价卖单
                    order = self.spot_client.order_market_sell(
                        symbol=symbol,
                        quantity=str(quantity)
                    )
                    logger.info(f"现货平仓成功: {order['orderId']}")
                    successful_liquidations += 1
                    time.sleep(1)  # 避免API限制

            except BinanceAPIException as e:
                logger.error(f"平仓现货 {asset} 失败: {e}")
                failed_liquidations.append((asset, str(e)))
            except Exception as e:
                logger.error(f"平仓现货 {asset} 出错: {e}")
                failed_liquidations.append((asset, str(e)))

        # 总结结果
        logger.info(f"现货平仓结果: 成功 {successful_liquidations} 个，失败 {len(failed_liquidations)} 个")
        if failed_liquidations:
            for asset, reason in failed_liquidations:
                logger.error(f"  {asset}: {reason}")

        return len(failed_liquidations) == 0

    def get_futures_positions(self) -> Dict[str, dict]:
        """获取期货持仓信息"""
        try:
            positions = self.futures_client.get_position_risk()
            result = {}

            for position in positions:
                symbol = position['symbol']
                position_amt = Decimal(position['positionAmt'])

                if position_amt != 0:  # 只关注有持仓的
                    result[symbol] = {
                        'symbol': symbol,
                        'positionAmt': position_amt,
                        'markPrice': Decimal(position['markPrice']),
                        'unRealizedProfit': Decimal(position['unRealizedProfit']),
                        'positionSide': position['positionSide']
                    }

            logger.info(f"获取到 {len(result)} 个期货持仓")
            return result
        except BinanceAPIException as e:
            logger.error(f"获取期货持仓失败: {e}")
            raise

    def liquidate_futures_positions(self, assets_to_liquidate: List[str], dry_run: bool = True) -> bool:
        """平仓指定币种的期货持仓"""
        if not assets_to_liquidate:
            return True

        logger.info("开始平仓期货持仓...")

        # 获取当前期货持仓
        futures_positions = self.get_futures_positions()

        if not futures_positions:
            logger.info("没有期货持仓需要平仓")
            return True

        successful_liquidations = 0
        failed_liquidations = []

        for asset in assets_to_liquidate:
            # 查找相关的期货持仓
            related_positions = []
            for symbol, position in futures_positions.items():
                if symbol.startswith(asset):  # 例如 BTCUSDT, BTCUSDC
                    related_positions.append((symbol, position))

            if not related_positions:
                logger.info(f"{asset} 没有相关的期货持仓")
                continue

            # 平仓所有相关持仓
            for symbol, position in related_positions:
                try:
                    position_amt = position['positionAmt']

                    if position_amt == 0:
                        continue

                    # 确定平仓方向
                    side = 'BUY' if position_amt < 0 else 'SELL'
                    quantity = abs(position_amt)

                    logger.info(f"平仓期货 {symbol}: {side} 数量 {quantity}")

                    if dry_run:
                        logger.info(f"[模拟] 期货平仓: {symbol} {side} {quantity}")
                        successful_liquidations += 1
                    else:
                        # 执行期货平仓订单
                        order = self.futures_client.new_order(
                            symbol=symbol,
                            side=side,
                            type='MARKET',
                            quantity=str(quantity),
                            reduceOnly=True  # 只减仓
                        )
                        logger.info(f"期货平仓成功: {order}")
                        successful_liquidations += 1
                        time.sleep(1)  # 避免API限制

                except BinanceAPIException as e:
                    logger.error(f"平仓期货 {symbol} 失败: {e}")
                    failed_liquidations.append((symbol, str(e)))
                except Exception as e:
                    logger.error(f"平仓期货 {symbol} 出错: {e}")
                    failed_liquidations.append((symbol, str(e)))

        # 总结结果
        logger.info(f"期货平仓结果: 成功 {successful_liquidations} 个，失败 {len(failed_liquidations)} 个")
        if failed_liquidations:
            for symbol, reason in failed_liquidations:
                logger.error(f"  {symbol}: {reason}")

        return len(failed_liquidations) == 0

    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"配置文件 {config_path} 不存在")
            raise
        except json.JSONDecodeError:
            logger.error(f"配置文件 {config_path} 格式错误")
            raise



    def get_spot_balances(self) -> Dict[str, Decimal]:
        account = self.spot_client.get_account()
        balances = account["balances"]
        # 过滤掉空余额，更直观
        non_zero = [
            b for b in balances
            if float(b["free"]) > 0 or float(b["locked"]) > 0
        ]
        logger.info(f"获取到 {len(non_zero)} 个账户余额")
        result = {}
        for b in non_zero:
            result[b["asset"]] = Decimal(b["free"]) + Decimal(b["locked"])
        return result

    def get_futures_account_info(self) -> dict:
        """获取统一账户信息"""
        ts = int(time.time()*1000)
        account_info = self.futures_client.get_account()
        logger.info(f"{account_info}")
        assets = account_info['assets']
        usdt = {}
        for asset in assets:
            if asset['asset'] == 'USDT':
                usdt = asset
                break
        if 'crossWalletBalance' in usdt:
            logger.info(f"总钱包余额: {usdt['crossWalletBalance']} USDT")

        return usdt

    def get_spot_prices(self, symbols: List[str]) -> Dict[str, Decimal]:
        """获取现货价格信息"""
        try:
            prices = {}
            ticker_prices = self.spot_client.get_all_tickers()

            for ticker in ticker_prices:
                symbol = ticker['symbol']
                if symbol in symbols:
                    prices[symbol] = Decimal(ticker['price'])

            return prices
        except BinanceAPIException as e:
            logger.error(f"获取现货价格信息失败: {e}")
            raise

    def get_available_futures_symbols(self) -> Dict[str, dict]:
        """获取可用的期货交易对信息"""
        try:
            exchange_info = self.futures_client.exchange_info()
            futures_symbols = {}

            for symbol_info in exchange_info['symbols']:
                if symbol_info['status'] == 'TRADING' and symbol_info['contractType'] == 'PERPETUAL':
                    base_asset = symbol_info['baseAsset']
                    quote_asset = symbol_info['quoteAsset']
                    futures_symbols[f"{base_asset}{quote_asset}"] = symbol_info

            logger.info(f"获取到 {len(futures_symbols)} 个可用期货交易对")
            return futures_symbols
        except BinanceAPIException as e:
            logger.error(f"获取期货交易对信息失败: {e}")
            raise

    def calculate_spot_usdt_values(self, balances: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """计算每个币种的USDT价值"""
        usdt_values = {}

        # USDT本身
        if 'USDT' in balances:
            usdt_values['USDT'] = balances['USDT']

        # 其他币种需要获取对USDT的价格
        symbols_needed = []
        for asset in balances:
            if asset != 'USDT':
                symbols_needed.append(f"{asset}USDT")

        if symbols_needed:
            prices = self.get_spot_prices(symbols_needed)

            for asset, balance in balances.items():
                if asset == 'USDT':
                    continue

                symbol = f"{asset}USDT"
                if symbol in prices:
                    usdt_values[asset] = balance * prices[symbol]
                    logger.info(f"{asset}: {balance} * {prices[symbol]} = {usdt_values[asset]} USDT")
                else:
                    logger.warning(f"无法获取 {symbol} 的价格")

        return usdt_values

    def calculate_target_allocation(self, usdt_values: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """计算目标分配 - 只为目标币种分配，每个币种20 USDT，其余转为USDT"""
        if not self.target_assets:
            logger.error("目标币种列表为空，无法计算目标分配")
            return {}

        total_value = sum(usdt_values.values())

        # 只考虑目标币种中的非USDT币种
        target_non_usdt_assets = self.target_assets - {'USDT'}
        num_target_assets = len(target_non_usdt_assets)

        # 计算每个非USDT目标币种应该持有的价值
        target_value_per_asset = self.target_position_usdt

        # 计算剩余价值（应该转为USDT）
        remaining_value = total_value - (target_value_per_asset * num_target_assets)

        logger.info(f"总价值: {total_value} USDT")
        logger.info(f"目标币种(非USDT): {', '.join(sorted(target_non_usdt_assets))}")
        logger.info(f"目标币种数量: {num_target_assets}")
        logger.info(f"每个币种目标价值: {target_value_per_asset} USDT")
        logger.info(f"剩余价值(转为USDT): {remaining_value} USDT")

        # 设置目标分配 - 只为目标币种设置分配
        target_allocation = {}

        # 为目标币种设置分配
        for asset in self.target_assets:
            if asset == 'USDT':
                target_allocation[asset] = remaining_value
                logger.info(f"目标分配 {asset}: {remaining_value} USDT")
            else:
                target_allocation[asset] = target_value_per_asset
                logger.info(f"目标分配 {asset}: {target_value_per_asset} USDT")

        # 为当前持有但不在目标列表中的币种设置为0（将被平仓）
        for asset in usdt_values:
            if asset not in self.target_assets:
                target_allocation[asset] = Decimal('0')
                logger.info(f"目标分配 {asset}: 0 USDT (将被平仓)")

        logger.info(f"目标分配总计: {sum(target_allocation.values())} USDT")
        return target_allocation

    def calculate_trades(self, balances: Dict[str, Decimal], usdt_values: Dict[str, Decimal],
                        target_allocation: Dict[str, Decimal]) -> List[Dict]:
        """计算需要执行的交易"""
        trades = []

        # 获取当前价格 - 需要获取所有可能交易的币种的价格
        symbols_needed = []
        all_assets = set(balances.keys()) | set(target_allocation.keys())
        for asset in all_assets:
            if asset != 'USDT':
                symbols_needed.append(f"{asset}USDT")

        prices = self.get_spot_prices(symbols_needed) if symbols_needed else {}

        # 遍历所有目标币种，确保余额为0的币种也被考虑
        all_assets = set(usdt_values.keys()) | set(target_allocation.keys())

        for asset in all_assets:
            current_value = usdt_values.get(asset, Decimal('0'))
            target_value = target_allocation.get(asset, Decimal('0'))
            difference = target_value - current_value

            logger.info(f"币种 {asset}: 当前价值 {current_value} USDT, 目标价值 {target_value} USDT, 差额 {difference} USDT")

            # 检查是否需要交易（超过容差）
            if abs(difference) > self.min_trade_amount:
                if asset == 'USDT':
                    # USDT的处理会在其他币种交易完成后自动调整
                    logger.info(f"跳过 USDT 交易，差额: {difference} USDT")
                    continue
                else:
                    # 其他币种
                    symbol = f"{asset}USDT"
                    if symbol in prices:
                        if difference > 0:  # 需要买入
                            logger.info(f"需要买入 {asset}: {difference} USDT")
                            trades.append({
                                'action': 'buy',
                                'symbol': symbol,
                                'usdt_amount': difference,
                                'asset': asset,
                                'price': prices[symbol]
                            })
                        else:  # 需要卖出
                            logger.info(f"需要卖出 {asset}: {abs(difference)} USDT")
                            trades.append({
                                'action': 'sell',
                                'symbol': symbol,
                                'usdt_amount': abs(difference),
                                'asset': asset,
                                'price': prices[symbol]
                            })
                    else:
                        logger.warning(f"无法获取 {symbol} 的价格，跳过交易")
            else:
                logger.info(f"币种 {asset} 差额 {difference} USDT 小于最小交易金额 {self.min_trade_amount} USDT，跳过")

        return trades

    def execute_spot_trade(self, trade: Dict) -> bool:
        """执行现货交易"""
        try:
            if trade['action'] == 'buy':
                # 买入
                symbol = trade['symbol']
                usdt_amount = trade['usdt_amount']

                exchange_info = self.spot_client.get_exchange_info()
                symbol_info = None
                for s in exchange_info['symbols']:
                    if s['symbol'] == symbol:
                        symbol_info = s
                        break
                if not symbol_info:
                    raise Exception(f"找不到交易对 {symbol}")

                # 计算数量（考虑最小数量限制）
                quantity = Decimal(str(usdt_amount / trade['price']))

                # 应用数量精度和检查最小名义价值
                min_notional = None
                for filter_item in symbol_info['filters']:
                    if filter_item['filterType'] == 'LOT_SIZE':
                        step_size = Decimal(filter_item['stepSize'])
                        # 按照 step_size 进行量化：先除以 step_size，向下取整，再乘以 step_size
                        quantity = (quantity / step_size).quantize(Decimal('1'), rounding=ROUND_DOWN) * step_size
                    elif filter_item['filterType'] == 'MIN_NOTIONAL':
                        min_notional = Decimal(filter_item['minNotional'])

                # 检查最小名义价值
                notional_value = quantity * Decimal(str(trade['price']))
                if min_notional and notional_value < min_notional:
                    logger.warning(f"交易金额 {notional_value} USDT 小于最小名义价值 {min_notional} USDT，跳过交易")
                    return False

                logger.info(f"买入 {symbol}: 数量 {quantity}, 预估价值 {usdt_amount} USDT, 名义价值 {notional_value} USDT")

                # 执行市价买单
                order = self.spot_client.order_market_buy(
                    symbol=symbol,
                    quantity=str(quantity)
                )

                logger.info(f"现货买单成功: {order['orderId']}")
                return True

            elif trade['action'] == 'sell':
                # 卖出
                symbol = trade['symbol']
                usdt_amount = trade['usdt_amount']

                # 获取现货交易对信息
                symbol_info = self.spot_client.get_symbol_info(symbol)

                # 计算数量
                quantity = Decimal(str(usdt_amount / trade['price']))

                # 应用数量精度和检查最小名义价值
                min_notional = None
                logger.info(f"交易对 {symbol} 的过滤器:")
                for filter_item in symbol_info['filters']:
                    logger.info(f"  过滤器类型: {filter_item['filterType']}")
                    if filter_item['filterType'] == 'LOT_SIZE':
                        step_size = Decimal(filter_item['stepSize'])
                        logger.info(f"step_size: {step_size} quantity: {quantity}")
                        # 按照 step_size 进行量化：先除以 step_size，四舍五入，再乘以 step_size
                        quantity = (quantity / step_size).quantize(Decimal('1'), rounding=ROUND_HALF_UP) * step_size
                        logger.info(f"quantity: {quantity}")
                    elif filter_item['filterType'] == 'MIN_NOTIONAL':
                        min_notional = Decimal(filter_item['minNotional'])
                        logger.info(f"找到 MIN_NOTIONAL: {min_notional}")
                    elif filter_item['filterType'] == 'NOTIONAL':
                        # 新版本的 NOTIONAL 过滤器可能有不同的字段
                        if 'minNotional' in filter_item:
                            min_notional = Decimal(filter_item['minNotional'])
                            logger.info(f"找到 NOTIONAL.minNotional: {min_notional}")
                        logger.info(f"NOTIONAL 过滤器内容: {filter_item}")

                # 检查最小名义价值
                notional_value = quantity * Decimal(str(trade['price']))
                logger.info(f"计算的名义价值: {notional_value}, 最小名义价值: {min_notional}")
                if min_notional and notional_value < min_notional:
                    logger.warning(f"交易金额 {notional_value} USDT 小于最小名义价值 {min_notional} USDT，跳过交易")
                    return False

                logger.info(f"卖出 {symbol}: 数量 {quantity}, 预估价值 {usdt_amount} USDT, 名义价值 {notional_value} USDT")

                # 执行市价卖单
                order = self.spot_client.order_market_sell(
                    symbol=symbol,
                    quantity=str(quantity)
                )

                logger.info(f"现货卖单成功: {order['orderId']}")
                return True

        except BinanceAPIException as e:
            logger.error(f"现货交易执行失败: {e}")
            return False
        except Exception as e:
            logger.error(f"现货交易执行出错: {e}")
            return False

    def find_futures_symbol_for_asset(self, asset: str, futures_symbols: Dict[str, dict]) -> Optional[str]:
        """为现货资产找到合适的期货交易对"""
        # 优先使用USDT计价的期货
        usdt_symbol = f"{asset}USDT"
        if usdt_symbol in futures_symbols:
            return usdt_symbol

        # 其次使用USDC计价的期货
        usdc_symbol = f"{asset}USDC"
        if usdc_symbol in futures_symbols:
            return usdc_symbol

        return None

    def check_futures_balance(self, quote_asset: str) -> Decimal:
        balances = self.futures_client.balance(recvWindow=6_000)          # list[dict]
        usdt = next(b for b in balances if b["asset"] == "USDT")
        logger.info(f"总余额       : {usdt['balance']} USDT")
        logger.info(f"可用余额     : {usdt['availableBalance']} USDT")
        return Decimal(usdt['availableBalance'])

    def calculate_hedge_quantity(self, symbol: str, balance: Decimal, futures_symbols: Dict[str, dict]) -> Decimal:
        """计算对冲数量，考虑最小下单量等限制"""
        symbol_info = futures_symbols.get(symbol)
        if not symbol_info:
            return balance

        # 获取数量精度
        quantity_precision = 0
        for filter_info in symbol_info.get('filters', []):
            if filter_info['filterType'] == 'LOT_SIZE':
                step_size = Decimal(filter_info['stepSize'])
                # 计算精度位数
                if step_size < 1:
                    quantity_precision = len(str(step_size).split('.')[-1].rstrip('0'))
                break

        # 按精度调整数量
        if quantity_precision > 0:
            return balance.quantize(Decimal('0.' + '0' * (quantity_precision - 1) + '1'), rounding=ROUND_DOWN)
        else:
            return balance.quantize(Decimal('1'), rounding=ROUND_DOWN)

    def place_futures_hedge_order(self, symbol: str, quantity: Decimal, futures_symbols: Dict[str, dict], dry_run: bool = True) -> bool:
        """下期货对冲订单（做空）"""
        try:
            # 检查当前期货持仓，避免重复对冲
            self.futures_client.change_leverage(symbol=symbol, leverage=TARGET_LEVERAGE)
            current_positions = self.get_futures_positions()
            if symbol in current_positions:
                current_position = current_positions[symbol]
                current_size = abs(Decimal(current_position['positionAmt']))

                if current_size > 0:
                    logger.info(f"{symbol} 已有期货持仓 {current_size}，检查是否需要调整")

                    # 计算目标对冲数量
                    target_hedge_quantity = self.calculate_hedge_quantity(symbol, quantity, futures_symbols)

                    if target_hedge_quantity <= 0:
                        logger.error(f"计算的目标对冲数量无效: {target_hedge_quantity}")
                        return False

                    # 计算需要调整的数量
                    quantity_diff = target_hedge_quantity - current_size

                    if abs(quantity_diff) < Decimal('0.001'):  # 差异很小，不需要调整
                        logger.info(f"{symbol} 当前持仓 {current_size} 接近目标 {target_hedge_quantity}，无需调整")
                        return True

                    if quantity_diff > 0:
                        # 需要增加空头持仓
                        hedge_quantity = quantity_diff
                        logger.info(f"{symbol} 需要增加空头持仓 {hedge_quantity}")
                    else:
                        # 需要减少空头持仓（平仓部分）
                        hedge_quantity = abs(quantity_diff)
                        logger.info(f"{symbol} 需要减少空头持仓 {hedge_quantity}")
                        # 这里应该执行平仓操作，暂时跳过
                        logger.warning(f"{symbol} 当前持仓过多，需要手动调整")
                        return False
                else:
                    # 当前无持仓，正常建立对冲
                    hedge_quantity = self.calculate_hedge_quantity(symbol, quantity, futures_symbols)
            else:
                # 当前无持仓，正常建立对冲
                hedge_quantity = self.calculate_hedge_quantity(symbol, quantity, futures_symbols)

            if hedge_quantity <= 0:
                logger.error(f"计算的对冲数量无效: {hedge_quantity}")
                return False

            if dry_run:
                logger.info(f"[模拟] 期货做空订单: {symbol}, 数量: {hedge_quantity}")
                return True

            # 实际下单
            try:
                order = self.futures_client.new_order(
                    symbol=symbol,
                    side='SELL',  # 做空对冲
                    type='MARKET',
                    quantity=str(hedge_quantity)
                )

                logger.info(f"期货做空订单成功: {order}")
                return True
            except ClientError as e:
                logger.error(f"期货下单失败 {symbol}: {e}")
                return False

        except BinanceAPIException as e:
            logger.error(f"期货下单失败 {symbol}: {e}")
            return False

    def balance_portfolio(self, dry_run: bool = True, enable_hedge: bool = True):
        """执行投资组合平衡和对冲"""
        logger.info("开始投资组合平衡...")

        # 检查是否设置了目标币种
        if not self.target_assets:
            logger.error("未设置目标币种列表，请先调用 set_target_assets() 方法")
            return

        # 1. 获取当前余额
        logger.info("获取现货账户余额...")
        balances = self.get_spot_balances()

        if not balances:
            logger.warning("没有发现任何现货余额")
            return

        # 2. 识别需要平仓的币种
        logger.info("识别需要平仓的币种...")
        assets_to_liquidate = self.identify_assets_to_liquidate(balances)

        # 3. 平仓不在目标列表中的现货和期货
        if assets_to_liquidate:
            logger.info("\n=== 开始平仓操作 ===")

            # 3a. 平仓期货持仓
            logger.info("平仓期货持仓...")
            futures_liquidation_success = self.liquidate_futures_positions(assets_to_liquidate, dry_run)

            # 3b. 平仓现货持仓
            logger.info("平仓现货持仓...")
            spot_liquidation_success = self.liquidate_spot_positions(assets_to_liquidate, dry_run)

            if not (futures_liquidation_success and spot_liquidation_success):
                logger.warning("部分平仓操作失败，但继续执行后续操作")

            # 重新获取余额（平仓后可能发生变化）
            logger.info("重新获取现货账户余额...")
            balances = self.get_spot_balances()

        # 4. 计算USDT价值
        logger.info("计算现货持仓USDT价值...")
        usdt_values = self.calculate_spot_usdt_values(balances)

        # 5. 计算目标分配
        logger.info("计算目标分配...")
        target_allocation = self.calculate_target_allocation(usdt_values)

        # 6. 计算需要的交易
        logger.info("计算需要的现货交易...")
        trades = self.calculate_trades(balances, usdt_values, target_allocation)

        if trades:
            # 7. 显示交易计划
            logger.info("\n=== 现货交易计划 ===")
            for i, trade in enumerate(trades, 1):
                logger.info(f"{i}. {trade}")

            # 8. 执行现货交易
            if dry_run:
                logger.info("这是模拟运行，不会执行实际现货交易")
            else:
                logger.info("开始执行现货交易...")
                for i, trade in enumerate(trades, 1):
                    logger.info(f"执行现货交易 {i}/{len(trades)}")
                    success = self.execute_spot_trade(trade)
                    if success:
                        logger.info(f"现货交易 {i} 成功")
                        time.sleep(1)  # 避免API限制
                    else:
                        logger.error(f"现货交易 {i} 失败")
        else:
            logger.info("现货投资组合已经平衡，无需交易")

        # 9. 执行期货对冲（如果启用）
        if enable_hedge:
            logger.info("\n=== 开始执行期货对冲 ===")
            self.execute_futures_hedge(dry_run)

        logger.info("投资组合平衡和对冲完成")

    def hedge_only(self, dry_run: bool = True):
        """只执行期货对冲，不执行现货平衡"""
        logger.info("开始执行期货对冲（仅对冲模式）...")

        # 检查是否设置了目标币种
        if not self.target_assets:
            logger.error("未设置目标币种列表，请先调用 set_target_assets() 方法")
            return

        # 直接执行期货对冲
        self.execute_futures_hedge(dry_run)
        logger.info("期货对冲完成")

    def execute_futures_hedge(self, dry_run: bool = True):
        """执行期货对冲策略"""
        logger.info("开始执行期货对冲策略...")

        # 1. 重新获取现货余额（可能在交易后发生变化）
        logger.info("获取最新现货账户余额...")
        spot_balances = self.get_spot_balances()

        if not spot_balances:
            logger.warning("没有发现任何现货余额")
            return

        # 2. 计算现货持仓USDT价值
        logger.info("计算现货持仓USDT价值...")
        usdt_values = self.calculate_spot_usdt_values(spot_balances)

        # 3. 找出需要对冲的持仓（排除稳定币，只对冲目标金额的持仓）
        logger.info("筛选需要对冲的持仓...")
        hedgeable_positions = []

        for asset, usdt_value in usdt_values.items():
            if asset not in self.stable_coins and usdt_value > 0:
                balance = spot_balances[asset]
                # 对冲实际持仓价值，但最多不超过目标金额
                hedge_value = min(usdt_value, self.target_position_usdt)
                hedge_balance = balance * (hedge_value / usdt_value)
                hedgeable_positions.append((asset, hedge_balance, hedge_value))
                logger.info(f"需要对冲 {asset}: {hedge_balance} (价值 {hedge_value} USDT)")

        if not hedgeable_positions:
            logger.info("没有发现需要对冲的持仓")
            return

        # 4. 获取期货交易对信息
        logger.info("获取期货交易对信息...")
        futures_symbols = self.get_available_futures_symbols()

        # 5. 为每个持仓执行对冲
        successful_hedges = 0
        failed_hedges = []

        for asset, balance, usdt_value in hedgeable_positions:
            logger.info(f"\n处理 {asset} 持仓对冲...")

            # 查找合适的期货交易对
            futures_symbol = self.find_futures_symbol_for_asset(asset, futures_symbols)

            if not futures_symbol:
                logger.error(f"❌ {asset} 没有找到合适的期货交易对")
                failed_hedges.append((asset, "没有期货交易对"))
                continue

            # 检查期货账户余额
            quote_asset = futures_symbol.replace(asset, '')  # 提取报价币种
            futures_balance = self.check_futures_balance(quote_asset)

            logger.info(f"期货账户 {quote_asset} 余额: {futures_balance}")

            # 检查余额是否足够
            if futures_balance * TARGET_LEVERAGE < usdt_value:
                error_msg = f"期货账户 {quote_asset} 余额不足: {futures_balance} < {usdt_value}"
                logger.error(f"❌ {error_msg}")
                failed_hedges.append((asset, error_msg))
                continue

            # 下对冲订单
            logger.info(f"在期货市场做空 {futures_symbol}，对冲现货 {asset} 持仓")

            if self.place_futures_hedge_order(futures_symbol, balance, futures_symbols, dry_run):
                successful_hedges += 1
                logger.info(f"✅ {asset} 对冲成功")
            else:
                failed_hedges.append((asset, "下单失败"))

        # 6. 总结结果
        logger.info(f"\n=== 对冲结果总结 ===")
        logger.info(f"成功对冲: {successful_hedges} 个持仓")

        if failed_hedges:
            logger.info(f"失败对冲: {len(failed_hedges)} 个持仓")
            for asset, reason in failed_hedges:
                logger.error(f"  {asset}: {reason}")

        if not successful_hedges and failed_hedges:
            logger.warning("所有对冲操作都失败了！请检查期货账户余额和交易对可用性。")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(
        description='Binance现货持仓平衡和期货对冲工具',
        epilog='''
使用示例:
  python balance_portfolio.py                    # 模拟运行：现货平衡 + 期货对冲
  python balance_portfolio.py --live             # 实际交易：现货平衡 + 期货对冲
  python balance_portfolio.py --no-hedge         # 只执行现货平衡，不对冲
  python balance_portfolio.py --hedge-only       # 只执行期货对冲，不平衡现货
  python balance_portfolio.py --hedge-only --live # 实际执行期货对冲
        ''',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('--config', default='portfolio_config.json', help='配置文件路径')
    parser.add_argument('--live', default=True, action='store_true', help='执行实际交易（默认为模拟运行）')
    parser.add_argument('--no-hedge', action='store_true', help='禁用期货对冲，只执行现货平衡')
    parser.add_argument('--hedge-only', default=False, action='store_true', help='只执行期货对冲，不执行现货平衡')
    parser.add_argument('--assets', nargs='+', help='目标持仓币种列表，例如: --assets BTC ETH BNB')

    args = parser.parse_args()

    balancer = PortfolioBalancer(args.config)

    # 获取目标币种列表
    # target_assets = []
    # if args.assets:
    #     target_assets = args.assets
    # else:
    #     # 交互式输入
    #     print("请输入要持有的币种列表（用空格分隔，例如: BTC ETH BNB）:")
    #     user_input = input().strip()
    #     if user_input:
    #         target_assets = user_input.upper().split()
    #     else:
    #         logger.error("未指定目标币种列表")
    #         return 1

    # if not target_assets:
    #     logger.error("目标币种列表不能为空")
    #     return 1

    # 设置目标币种
    target_assets = ["SOL", "XRP", "BNB", "ETH", "SUI", "BCH", "APT", "ETC", "NEAR", "INJ", "ADA", "LTC", "USDT", "USDC", "DOT", "XLM", "HBAR", "POL", "UNI", "SEI", "BTC"]
    balancer.set_target_assets(target_assets)

    # 检查参数冲突
    if args.hedge_only and args.no_hedge:
        logger.error("--hedge-only 和 --no-hedge 参数不能同时使用")
        return 1

    # 执行操作
    if args.hedge_only:
        # 只执行期货对冲
        balancer.hedge_only(dry_run=not args.live)
    else:
        # 执行完整的平衡操作（包括现货平衡和可选的期货对冲）
        balancer.balance_portfolio(dry_run=not args.live, enable_hedge=not args.no_hedge)

    return 0

if __name__ == '__main__':
    exit(main())
