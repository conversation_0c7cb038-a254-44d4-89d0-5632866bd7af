import asyncio
import time
from binance import AsyncClient, BinanceSocketManager
from binance.exceptions import BinanceAPIException
import pandas as pd
import numpy as np
from aiohttp import ClientConnectorError, ServerDisconnectedError, ClientOSError
from typing import List, Tuple
import socket

# === 配置区域 ===
API_KEY = "YOUR_API_KEY"  # 只读行情可不填
API_SECRET = "YOUR_API_SECRET"  # 只读行情可不填

QUOTE_ASSETS = {"USDT", "BUSD", "BTC", "ETH"}  # 只统计这些计价的交易对
INTERVAL = "15m"  # 15 分钟线
DAYS = 7  # 最近 7 天
CANDLES_PER_DAY = int(24 * 60 / 15)
LIMIT = DAYS * CANDLES_PER_DAY  # 7 天 * 96 = 672，<=1000
CONCURRENCY = 5  # 并发抓取任务数 (降低以减少连接问题)
RETRY_LIMIT = 5  # 单个请求最多重试次数 (增加重试次数)
RATE_LIMIT_DELAY = 0.5  # 每个请求间隔(秒)，防止被限速 (增加延迟)


# === 异步抓取单个交易对 K 线并计算波动率 ===
async def fetch_volatility(client: AsyncClient, symbol: str) -> Tuple[str, float]:
    for attempt in range(1, RETRY_LIMIT + 1):
        try:
            klines = await client.get_klines(symbol=symbol, interval=INTERVAL, limit=LIMIT)
            closes = np.array([float(k[4]) for k in klines])
            # 计算对数收益率
            log_ret = np.diff(np.log(closes))
            vol = float(np.std(log_ret, ddof=1))  # 样本标准差
            return symbol, vol
        except (
            BinanceAPIException,
            ClientConnectorError,
            ServerDisconnectedError,
            ClientOSError,
            ConnectionResetError,
            socket.error,
            OSError,
            Exception,
        ) as e:
            if attempt == RETRY_LIMIT:
                print(f"[ERROR] {symbol} 重试 {attempt} 次仍失败：{e}")
                return symbol, np.nan

            # 指数退避策略
            wait_time = min(2**attempt, 30)  # 最多等待30秒
            print(f"[RETRY] {symbol} 第 {attempt}/{RETRY_LIMIT} 次重试失败: {type(e).__name__}: {e}")
            print(f"        等待 {wait_time} 秒后重试...")
            await asyncio.sleep(wait_time)
    return symbol, np.nan


# === 检查 price_tick 占比 ===
async def check_price_tick_ratio(client: AsyncClient, symbol: str, price_tick: float) -> bool:
    """检查 price_tick 占当前价格的占比是否小于 0.001"""
    for attempt in range(1, RETRY_LIMIT + 1):
        try:
            ticker = await client.get_symbol_ticker(symbol=symbol)
            current_price = float(ticker["price"])
            if current_price > 0:
                ratio = price_tick / current_price
                return ratio < 0.001
            return False
        except (
            BinanceAPIException,
            ClientConnectorError,
            ServerDisconnectedError,
            ClientOSError,
            ConnectionResetError,
            socket.error,
            OSError,
            Exception,
        ) as e:
            if attempt == RETRY_LIMIT:
                print(f"[WARNING] 获取 {symbol} 当前价格重试 {attempt} 次仍失败：{e}")
                return False

            # 指数退避策略
            wait_time = min(2**attempt, 10)  # 价格检查等待时间较短
            print(f"[RETRY] {symbol} 价格获取第 {attempt}/{RETRY_LIMIT} 次重试失败: {type(e).__name__}")
            await asyncio.sleep(wait_time)
    return False


# === 主流程 ===
async def main():
    client = None
    try:
        # 创建客户端时也添加重试逻辑
        for attempt in range(1, RETRY_LIMIT + 1):
            try:
                client = await AsyncClient.create(API_KEY, API_SECRET)
                break
            except Exception as e:
                if attempt == RETRY_LIMIT:
                    print(f"[ERROR] 创建客户端失败，重试 {attempt} 次仍失败：{e}")
                    return
                wait_time = min(2**attempt, 10)
                print(f"[RETRY] 创建客户端第 {attempt}/{RETRY_LIMIT} 次失败，等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)

        # 获取交易所信息时也添加重试逻辑
        info = None
        for attempt in range(1, RETRY_LIMIT + 1):
            try:
                info = await client.get_exchange_info()
                break
            except Exception as e:
                if attempt == RETRY_LIMIT:
                    print(f"[ERROR] 获取交易所信息失败，重试 {attempt} 次仍失败：{e}")
                    return
                wait_time = min(2**attempt, 10)
                print(f"[RETRY] 获取交易所信息第 {attempt}/{RETRY_LIMIT} 次失败，等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)

        # 筛选现货可交易交易对
        symbols = [
            s["symbol"]
            for s in info["symbols"]
            if s["status"] == "TRADING" and s["isSpotTradingAllowed"] and s["quoteAsset"] in QUOTE_ASSETS
        ]
        print(f"共找到 {len(symbols)} 个现货交易对，准备并发抓取波动率...")

        sem = asyncio.Semaphore(CONCURRENCY)

        async def sem_task(sym):
            async with sem:
                await asyncio.sleep(RATE_LIMIT_DELAY)
                return await fetch_volatility(client, sym)

        tasks = [asyncio.create_task(sem_task(sym)) for sym in symbols]

        # 使用 return_exceptions=True 来避免单个任务失败影响整体
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤掉异常结果
        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                print(f"[WARNING] 任务执行异常: {result}")
            else:
                valid_results.append(result)

    except Exception as e:
        print(f"[ERROR] 主流程执行失败：{e}")
        import traceback

        traceback.print_exc()
    finally:
        if client:
            await client.close_connection()

    # 转 DataFrame，筛掉失败项，排序取前 15
    df = pd.DataFrame(results, columns=["symbol", "volatility"]).dropna()
    df = df.sort_values("volatility", ascending=False).reset_index(drop=True)
    top15 = df.head(15)

    print("\n过去一周（15m 级别）波动率最高的 15 个币种：")
    print(top15.to_markdown(index=False, floatfmt=".6f"))

    # 如果需要，将结果保存到本地 CSV
    top15.to_csv("top15_spot_volatility.csv", index=False)
    print("\n已将结果保存到 top15_spot_volatility.csv")


if __name__ == "__main__":
    asyncio.run(main())
